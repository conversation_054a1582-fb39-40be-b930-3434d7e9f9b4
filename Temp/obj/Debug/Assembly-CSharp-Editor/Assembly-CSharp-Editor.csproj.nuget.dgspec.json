{"format": 1, "restore": {"/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp-Editor.csproj": {}}, "projects": {"/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp-Editor.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/UnityProjects/Soul_game/Temp/obj/Debug/Assembly-CSharp-Editor/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.402/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/UnityProjects/Soul_game/Temp/obj/Debug/Assembly-CSharp/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.402/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj", "projectName": "FMODUnity", "projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/UnityProjects/Soul_game/Temp/obj/Debug/FMODUnity/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.402/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj", "projectName": "FMODUnityEditor", "projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/UnityProjects/Soul_game/Temp/obj/Debug/FMODUnityEditor/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.402/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj", "projectName": "FMODUnityResonance", "projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/UnityProjects/Soul_game/Temp/obj/Debug/FMODUnityResonance/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.402/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj", "projectName": "FMODUnityResonanceEditor", "projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/UnityProjects/Soul_game/Temp/obj/Debug/FMODUnityResonanceEditor/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.402/RuntimeIdentifierGraph.json"}}}}}