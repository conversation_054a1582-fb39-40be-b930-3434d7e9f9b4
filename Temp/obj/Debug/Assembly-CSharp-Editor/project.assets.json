{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FMODUnity": "1.0.0", "FMODUnityEditor": "1.0.0", "FMODUnityResonance": "1.0.0", "FMODUnityResonanceEditor": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "FMODUnity/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/FMODUnity.dll": {}}, "runtime": {"bin/placeholder/FMODUnity.dll": {}}}, "FMODUnityEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FMODUnity": "1.0.0"}, "compile": {"bin/placeholder/FMODUnityEditor.dll": {}}, "runtime": {"bin/placeholder/FMODUnityEditor.dll": {}}}, "FMODUnityResonance/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FMODUnity": "1.0.0"}, "compile": {"bin/placeholder/FMODUnityResonance.dll": {}}, "runtime": {"bin/placeholder/FMODUnityResonance.dll": {}}}, "FMODUnityResonanceEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FMODUnityResonance": "1.0.0"}, "compile": {"bin/placeholder/FMODUnityResonanceEditor.dll": {}}, "runtime": {"bin/placeholder/FMODUnityResonanceEditor.dll": {}}}}}, "libraries": {"Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "FMODUnity/1.0.0": {"type": "project", "path": "FMODUnity.csproj", "msbuildProject": "FMODUnity.csproj"}, "FMODUnityEditor/1.0.0": {"type": "project", "path": "FMODUnityEditor.csproj", "msbuildProject": "FMODUnityEditor.csproj"}, "FMODUnityResonance/1.0.0": {"type": "project", "path": "FMODUnityResonance.csproj", "msbuildProject": "FMODUnityResonance.csproj"}, "FMODUnityResonanceEditor/1.0.0": {"type": "project", "path": "FMODUnityResonanceEditor.csproj", "msbuildProject": "FMODUnityResonanceEditor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp >= 1.0.0", "FMODUnity >= 1.0.0", "FMODUnityEditor >= 1.0.0", "FMODUnityResonance >= 1.0.0", "FMODUnityResonanceEditor >= 1.0.0"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp-Editor.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/UnityProjects/Soul_game/Temp/obj/Debug/Assembly-CSharp-Editor/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/Assembly-CSharp.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnity.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityEditor.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonance.csproj"}, "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj": {"projectPath": "/Users/<USER>/UnityProjects/Soul_game/FMODUnityResonanceEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.402/RuntimeIdentifierGraph.json"}}}}