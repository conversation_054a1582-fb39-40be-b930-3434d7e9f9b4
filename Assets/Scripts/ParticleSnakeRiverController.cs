using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Unity.Jobs;
using Unity.Collections;
using Unity.Burst;

[RequireComponent(typeof(ParticleSystem))]
public class ParticleSnakeRiverController : MonoBehaviour
{
    [Header("Path Generation")]
    [Tooltip("Controls the frequency of Perlin noise for path randomness")]
    public float noiseScale = 0.05f;
    [Tooltip("Maximum horizontal variation of the path")]
    public float xAmplitude = 50;
    [Tooltip("Maximum vertical variation of the path (up to 20 units)")]
    public float yAmplitude = 50f;
    [Tooltip("Minimum height of the river path")]
    public float minY = -10f;
    [Tooltip("Maximum height of the river path")]
    public float maxY = 10f;
    [Tooltip("Distance between control points along the Z-axis")]
    public float controlSegmentLength = 10f;
    [Tooltip("Number of control points in each direction (total = 2N + 1)")]
    public int N = 100;
    [Tooltip("Interpolation step for spline path points")]
    public float pathStep = 0.1f;
    [Tooltip("Smoothing factor for local averaging of Perlin noise (higher = smoother)")]
    [Range(1, 10)]
    public int smoothingFactor = 3;
    [Tooltip("Seed value for reproducible random paths (0 = random seed each time)")]
    public int seed = 0;

    [Header("River Properties")]
    [Tooltip("Radius of the cylindrical river")]
    public float riverRadius = 5f;
    [Tooltip("Speed at which particles flow along the path")]
    public float flowSpeed = 10f;

    [Header("Particle System")]
    [Tooltip("Particles emitted per second")]
    public float emitRate = 100f;
    [Tooltip("Lifetime of each particle in seconds")]
    public float particleLifetime = 10f;

    [Header("Culling")]
    [Tooltip("Distance from camera where particle culling starts (fade begins)")]
    public float cullDistanceStart = 100f;
    [Tooltip("Distance from camera where particles are fully culled (disappear)")]
    public float cullDistanceEnd = 300f;

    [Header("Debug")]
    [Tooltip("Toggle visualization of the path in the editor")]
    public bool visualizePath = true;

    [Header("Endless River")]
    [Tooltip("How far ahead of the camera to keep the river path (in world units)")]
    public float forwardExtension = 800f;
    [Tooltip("How far behind the camera to keep the river path (in world units)")]
    public float backwardExtension = 200f;

    [Header("Path Generation")]
    [Tooltip("Main direction of the river (randomized at start if zero)")]
    public Vector3 mainDirection = Vector3.zero;

    [Header("Player River Interaction")]
    [Tooltip("Maximum speed at which the river sweeps the player along its flow direction")]
    public float playerSweepMaxSpeed = 3f;
    [Tooltip("How strongly the river pulls the player towards its center")]
    public float playerCenterPullStrength = 0.25f;
    [Tooltip("How far outside the river radius the sweep effect starts to apply")]
    public float playerEffectFalloff = 6f;

    [Header("Particle Y Clamp")]
    [Tooltip("Minimum Y for particles (visual clamping, independent of path)")]
    private float minYperParticle = 20.0f;
    [Tooltip("Maximum Y for particles (visual clamping, independent of path)")]
    private float maxYperParticle = 50.0f;

    [Header("Performance")]
    [Tooltip("How often (in seconds) to update particle steering/culling. Lower is smoother, higher is more performant.")]
    public float particleUpdateInterval = 0.01f;
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    public float maxDeltaTime = 0.05f;

    private ParticleSystem riverParticleSystem;
    private List<Vector3> pathPoints;
    private List<Vector3> tangents;
    private float[] pathZ;
    private float emitTimer = 0f;
    private float particleUpdateTimer = 0f;

    // Path caching system
    private Dictionary<int, Vector3> cachedControlPoints = new Dictionary<int, Vector3>();
    private int currentSeed;

    private float minZ, maxZ;
    private int minControlIndex, maxControlIndex;

    // Internal direction for river flow
    private Vector3 _mainDirection;
    private Vector3 _mainDirectionNormalized;

    private PlayerController playerController;
    private Transform playerTransform;
    private bool playerWasInRiver = false;

    // Track freed particles by their randomSeed
    private HashSet<uint> freedParticleSeeds = new HashSet<uint>();

    private ParticleSystem.Particle[] particleBuffer;
    private List<int> eligibleIndicesBuffer = new List<int>();

    // NativeArray caches for jobs
    private NativeArray<ParticleSystem.Particle> nativeParticles;
    private NativeArray<Vector3> nativePathPoints;
    private NativeArray<float> nativePathZ;
    private NativeArray<uint> nativeFreedSeeds;

    void Start()
    {
        // Initialize the random generator with the specified seed or a random one
        InitializeRandomGenerator();
        // Randomize main direction if not set
        if (mainDirection == Vector3.zero)
        {
            float angle = Random.Range(0f, 2f * Mathf.PI);
            _mainDirection = new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle));
        }
        else
        {
            _mainDirection = mainDirection;
        }
        _mainDirectionNormalized = _mainDirection.normalized;
        // Get and configure particle system
        riverParticleSystem = GetComponent<ParticleSystem>();
        var main = riverParticleSystem.main;
        main.simulationSpace = ParticleSystemSimulationSpace.World;
        var emission = riverParticleSystem.emission;
        emission.rateOverTime = 0;
        // Set initial min/max indices for endless river
        minZ = -backwardExtension;
        maxZ = forwardExtension;
        minControlIndex = Mathf.FloorToInt(minZ / controlSegmentLength);
        maxControlIndex = Mathf.CeilToInt(maxZ / controlSegmentLength);
        GenerateRiverPathDynamic(minControlIndex, maxControlIndex);

        // Set particle clamp to bottom and top of ocean
        minYperParticle = GameManager.Instance.oceanBottom.position.y;
        maxYperParticle = GameManager.Instance.cloudsBottom.position.y;
    }

    // Initialize the random seed
    private void InitializeRandomGenerator()
    {
        // If seed is 0, use a random seed
        currentSeed = seed == 0 ? Random.Range(1, 100000) : seed;
        // The river controller should use its own deterministic generation without affecting global Random
    }

    /// <summary>
    /// Regenerates the river path with the current settings.
    /// Call this method if you change any path generation parameters at runtime.
    /// </summary>
    public void RegeneratePath()
    {
        // Re-initialize with current seed (but don't affect global Random)
        InitializeRandomGenerator();
        // Regenerate the river path for the current window
        GenerateRiverPathDynamic(minControlIndex, maxControlIndex);
    }

    void Update()
    {
        if (pathPoints == null || pathPoints.Count == 0) return;

        Vector3 cameraPosition = Camera.main.transform.position;
        float camDist = Vector3.Dot(cameraPosition, _mainDirectionNormalized);

        // Determine desired bounds based on camera position
        float desiredMinDist = camDist - backwardExtension;
        float desiredMaxDist = camDist + forwardExtension;
        int desiredMinIdx = Mathf.FloorToInt(desiredMinDist / controlSegmentLength);
        int desiredMaxIdx = Mathf.CeilToInt(desiredMaxDist / controlSegmentLength);

        // Extend river forward if needed
        if (desiredMaxIdx > maxControlIndex)
        {
            GenerateRiverPathDynamic(maxControlIndex + 1, desiredMaxIdx);
            maxControlIndex = desiredMaxIdx;
        }
        // Extend river backward if needed
        if (desiredMinIdx < minControlIndex)
        {
            GenerateRiverPathDynamic(desiredMinIdx, minControlIndex - 1);
            minControlIndex = desiredMinIdx;
        }

        // Prune points far behind/ahead
        PrunePathPoints(desiredMinDist, desiredMaxDist);

        // Determine eligible path points near the camera (reuse buffer)
        eligibleIndicesBuffer.Clear();
        for (int i = 0; i < pathPoints.Count; i++)
        {
            if (Vector3.Distance(pathPoints[i], cameraPosition) < cullDistanceEnd)
                eligibleIndicesBuffer.Add(i);
        }

        // Emit new particles with frame rate independent timing
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        emitTimer += clampedDeltaTime;
        int numToEmit = Mathf.FloorToInt(emitTimer * emitRate);
        if (numToEmit > 0)
        {
            emitTimer -= numToEmit / emitRate;
            // Prevent timer from accumulating too much during FPS drops
            emitTimer = Mathf.Min(emitTimer, 1f / emitRate);
        }
        for (int k = 0; k < numToEmit && eligibleIndicesBuffer.Count > 0; k++)
        {
            int idx = eligibleIndicesBuffer[Random.Range(0, eligibleIndicesBuffer.Count)];
            Vector3 P = pathPoints[idx];
            Vector3 T = tangents[idx];

            // Improved: Blend up vector with tangent for stable cross-section
            Vector3 upBlend = Vector3.Lerp(Vector3.up, T, 0.2f).normalized;
            Vector3 N = Vector3.Cross(T, upBlend).normalized;
            if (N.sqrMagnitude < 0.01f) N = Vector3.Cross(T, Vector3.right).normalized;
            Vector3 B = Vector3.Cross(T, N).normalized;
            float r = Random.Range(0, riverRadius);
            float theta = Random.Range(0, 2 * Mathf.PI);
            Vector3 offset = r * (Mathf.Cos(theta) * N + Mathf.Sin(theta) * B);

            // Set particle properties
            ParticleSystem.EmitParams emitParams = new ParticleSystem.EmitParams
            {
                position = P + offset,
                velocity = flowSpeed * T,
                startLifetime = particleLifetime
            };
            riverParticleSystem.Emit(emitParams, 1);
        }

        // --- Throttled heavy-duty particle update ---
        particleUpdateTimer += clampedDeltaTime;
        if (particleUpdateTimer >= particleUpdateInterval)
        {
            int particleCount = riverParticleSystem.particleCount;
            // Skip update if there are too many particles (performance protection)
            if (particleCount > 10000)
            {
                particleUpdateTimer = 0f;
                return;
            }

            if (particleBuffer == null || particleBuffer.Length < particleCount)
                particleBuffer = new ParticleSystem.Particle[particleCount];
            int count = riverParticleSystem.GetParticles(particleBuffer);

            // Cache/reuse NativeArrays for job using actual count
            if (nativeParticles.IsCreated) nativeParticles.Dispose();
            nativeParticles = new NativeArray<ParticleSystem.Particle>(count, Allocator.Persistent);
            if (nativePathPoints.IsCreated) nativePathPoints.Dispose();
            nativePathPoints = new NativeArray<Vector3>(pathPoints.Count, Allocator.Persistent);
            if (nativePathZ.IsCreated) nativePathZ.Dispose();
            nativePathZ = new NativeArray<float>(pathZ.Length, Allocator.Persistent);
            if (nativeFreedSeeds.IsCreated) nativeFreedSeeds.Dispose();
            nativeFreedSeeds = new NativeArray<uint>(freedParticleSeeds.Count, Allocator.Persistent);

            // Copy only the valid range
            for (int i = 0; i < count; i++) nativeParticles[i] = particleBuffer[i];
            for (int i = 0; i < pathPoints.Count; i++) nativePathPoints[i] = pathPoints[i];
            for (int i = 0; i < pathZ.Length; i++) nativePathZ[i] = pathZ[i];
            int idx = 0;
            foreach (var val in freedParticleSeeds) {
                if (idx < nativeFreedSeeds.Length) nativeFreedSeeds[idx++] = val;
            }

            var job = new ParticleUpdateJob
            {
                pathPoints = nativePathPoints,
                pathZ = nativePathZ,
                mainDirectionNormalized = _mainDirectionNormalized,
                riverRadius = riverRadius,
                flowSpeed = flowSpeed,
                minYperParticle = minYperParticle,
                maxYperParticle = maxYperParticle,
                cullDistanceStart = cullDistanceStart,
                cullDistanceEnd = cullDistanceEnd,
                cameraPosition = cameraPosition,
                freedParticleSeeds = nativeFreedSeeds,
                pathPointsCount = pathPoints.Count,
                particles = nativeParticles
            };
            var handle = job.Schedule(count, 64);
            handle.Complete();
            for (int i = 0; i < count; i++) particleBuffer[i] = nativeParticles[i];
            riverParticleSystem.SetParticles(particleBuffer, count);
            particleUpdateTimer -= particleUpdateInterval;
            // Prevent timer from accumulating too much during FPS drops
            particleUpdateTimer = Mathf.Max(0f, particleUpdateTimer);
        }

        // Player interaction logic (moved from LateUpdate)
        if (playerController == null || playerTransform == null)
        {
            if (GameManager.Instance != null && GameManager.Instance.player != null)
            {
                playerController = GameManager.Instance.player;
                playerTransform = playerController.transform;
            }
            else
            {
                var pc = FindFirstObjectByType<PlayerController>();
                if (pc != null)
                {
                    playerController = pc;
                    playerTransform = pc.transform;
                }
            }
        }
        if (playerController == null || playerTransform == null || pathPoints == null || pathPoints.Count < 2)
            return;
        // Find closest segment and point on river path
        Vector3 playerPos = playerTransform.position;
        int segIdx = FindSegmentIndex(playerPos);
        if (segIdx < 0 || segIdx >= pathPoints.Count - 1) return;
        float z0Player = pathZ[segIdx];
        float z1Player = pathZ[segIdx + 1];
        float playerDist = Vector3.Dot(playerPos, _mainDirectionNormalized);
        float tPlayer = Mathf.Clamp01((playerDist - z0Player) / (z1Player - z0Player));
        Vector3 riverCenter = Vector3.Lerp(pathPoints[segIdx], pathPoints[segIdx + 1], tPlayer);
        Vector3 riverTangent = (pathPoints[segIdx + 1] - pathPoints[segIdx]).normalized;
        float distToCenter = Vector3.Distance(playerPos, riverCenter);

        // Compute effect falloff (0 = outside, 1 = center)
        float effectRadius = riverRadius + playerEffectFalloff;
        float effectStrength = Mathf.Clamp01(1f - (distToCenter - riverRadius) / Mathf.Max(0.01f, playerEffectFalloff));
        bool inRiver = distToCenter <= riverRadius;
        bool inEffectZone = distToCenter <= effectRadius;

        // Gravity control
        if (inRiver)
        {
            playerController.SetGravityDisabled(true);
            playerWasInRiver = true;
        }
        else if (playerWasInRiver && !inRiver)
        {
            playerController.SetGravityDisabled(false);
            playerWasInRiver = false;
        }

        // Only apply forces if in effect zone
        if (inEffectZone)
        {
            // Pull to center (proportional to distance, diminishes as player approaches center)
            Vector3 toCenter = (riverCenter - playerPos);
            // Use the actual distance vector instead of normalized - this creates smooth, proportional pull
            // The force diminishes naturally as the player gets closer to the center
            // Clamp the maximum pull distance to prevent excessive forces when very far from center
            float maxPullDistance = riverRadius + playerEffectFalloff;
            if (toCenter.magnitude > maxPullDistance)
            {
                toCenter = toCenter.normalized * maxPullDistance;
            }
            Vector3 pull = toCenter * playerCenterPullStrength * effectStrength * clampedDeltaTime;

            // Sweep along river
            Vector3 sweep = riverTangent * playerSweepMaxSpeed * effectStrength * clampedDeltaTime;

            // Accumulate river movement for this frame
            if (playerController != null)
            {
                playerController.externalMovementThisFrame += pull + sweep;
            }
        }
    }

    // Generate or extend the river path between control indices (inclusive)
    private void GenerateRiverPathDynamic(int fromIdx, int toIdx)
    {
        if (fromIdx > toIdx) return;
        Vector3 origin = transform.position;
        // Ensure all control points in the full window are generated
        for (int j = minControlIndex; j <= maxControlIndex; j++)
        {
            if (!cachedControlPoints.ContainsKey(j))
            {
                Vector3 basePos = origin + _mainDirectionNormalized * (j * controlSegmentLength);
                float x = GenerateSmoothedPerlinValue(j, 0) * xAmplitude;
                float y = GenerateSmoothedPerlinValue(j, 1) * yAmplitude;
                y = Mathf.Clamp(y, minY, maxY);
                // Offset perpendicular to main direction for x
                Vector3 perp = Vector3.Cross(_mainDirectionNormalized, Vector3.up).normalized;
                Vector3 pos = basePos + perp * x;
                pos.y = y; // Set Y absolutely, not relative to basePos.y
                cachedControlPoints[j] = pos;
            }
        }

        // Rebuild pathPoints and tangents from minControlIndex to maxControlIndex
        List<Vector3> controlPoints = new List<Vector3>();
        for (int j = minControlIndex; j <= maxControlIndex; j++)
            controlPoints.Add(cachedControlPoints[j]);

        // Rebuild pathPoints
        List<Vector3> newPathPoints = new List<Vector3>();
        for (int j = 1; j < controlPoints.Count - 2; j++)
        {
            Vector3 p0 = controlPoints[j - 1];
            Vector3 p1 = controlPoints[j];
            Vector3 p2 = controlPoints[j + 1];
            Vector3 p3 = controlPoints[j + 2];
            for (float t = 0; t < 1; t += pathStep)
            {
                Vector3 point = CatmullRom(p0, p1, p2, p3, t);
                newPathPoints.Add(point);
            }
        }
        newPathPoints.Add(controlPoints[^2]); // Using index from end operator for clarity
        pathPoints = newPathPoints;

        // Rebuild tangents
        tangents = new List<Vector3>();
        for (int i = 0; i < pathPoints.Count; i++)
        {
            if (i == 0)
                tangents.Add((pathPoints[1] - pathPoints[0]).normalized);
            else if (i == pathPoints.Count - 1)
                tangents.Add((pathPoints[i] - pathPoints[i - 1]).normalized);
            else
                tangents.Add((pathPoints[i + 1] - pathPoints[i - 1]).normalized);
        }

        // Store distance along main direction for efficient segment lookup
        pathZ = pathPoints.Select(p => Vector3.Dot(p, _mainDirectionNormalized)).ToArray();
    }

    // Remove path points and control points far outside the current window
    private void PrunePathPoints(float minDist, float maxDist)
    {
        // Prune control points
        List<int> keysToRemove = new List<int>();
        foreach (var kvp in cachedControlPoints)
        {
            float dist = Vector3.Dot(kvp.Value, _mainDirectionNormalized);
            if (dist < minDist - 2 * controlSegmentLength || dist > maxDist + 2 * controlSegmentLength)
                keysToRemove.Add(kvp.Key);
        }
        foreach (int k in keysToRemove)
            cachedControlPoints.Remove(k);

        // Prune pathPoints (rebuild in GenerateRiverPathDynamic)
        // No need to prune here, as pathPoints is always rebuilt for the current window
    }

    // Generate control points with improved Perlin noise and local averaging
    private List<Vector3> GenerateControlPoints()
    {
        List<Vector3> controlPoints = new List<Vector3>();
        Vector3 origin = transform.position;
        for (int j = -N; j <= N; j++)
        {
            Vector3 basePos = origin + _mainDirectionNormalized * (j * controlSegmentLength);

            // Check if this control point is already cached
            if (cachedControlPoints.TryGetValue(j, out Vector3 cachedPoint))
            {
                controlPoints.Add(cachedPoint);
                continue;
            }

            // Apply local averaging for smoother curves
            float x = GenerateSmoothedPerlinValue(j, 0) * xAmplitude;
            float y = GenerateSmoothedPerlinValue(j, 1) * yAmplitude;
            y = Mathf.Clamp(y, minY, maxY);
            Vector3 perp = Vector3.Cross(_mainDirectionNormalized, Vector3.up).normalized;
            Vector3 controlPoint = basePos + perp * x;
            controlPoint.y = y; // Set Y absolutely
            cachedControlPoints[j] = controlPoint;
            controlPoints.Add(controlPoint);
        }
        return controlPoints;
    }

    // Generate a smoothed Perlin noise value using local averaging
    private float GenerateSmoothedPerlinValue(int index, int axis)
    {
        // Use the seed to create consistent noise
        float seedOffset = currentSeed * 0.1f;

        // Base Perlin noise value
        float sum = 0f;
        float weightSum = 0f;

        // Apply local averaging by sampling multiple nearby points
        for (int i = -smoothingFactor; i <= smoothingFactor; i++)
        {
            float samplePos = (index + i * 0.1f) * noiseScale;
            float weight = 1f - Mathf.Abs(i) / (float)(smoothingFactor + 1);

            // Get Perlin noise value (axis determines whether we're generating x or y)
            float noise = Mathf.PerlinNoise(samplePos + seedOffset, axis + seedOffset);

            // Normalize from [0,1] to [-1,1]
            noise = (noise - 0.5f) * 2f;

            // Add weighted contribution
            sum += noise * weight;
            weightSum += weight;
        }

        // Normalize the result
        return sum / weightSum;
    }

    // Catmull-Rom spline interpolation
    Vector3 CatmullRom(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;
        Vector3 a = 2f * p1;
        Vector3 b = p2 - p0;
        Vector3 c = 2f * p0 - 5f * p1 + 4f * p2 - p3;
        Vector3 d = -p0 + 3f * p1 - 3f * p2 + p3;
        return 0.5f * (a + (b * t) + (c * t2) + (d * t3));
    }

    // Find the path segment for a given position using binary search along the main direction
    int FindSegmentIndex(Vector3 pos)
    {
        float dist = Vector3.Dot(pos, _mainDirectionNormalized);
        int index = System.Array.BinarySearch(pathZ, dist);
        if (index >= 0) return index;
        int insertionPoint = ~index;
        if (insertionPoint == 0) return 0;
        if (insertionPoint == pathZ.Length) return pathZ.Length - 2;
        return insertionPoint - 1;
    }

#if UNITY_EDITOR
    // Allow updating the path in the editor when parameters change
    void OnValidate()
    {
        // Only regenerate if we're in play mode and have already initialized
        if (Application.isPlaying && riverParticleSystem != null)
        {
            // Regenerate the path with the new parameters
            RegeneratePath();
        }
    }
#endif

    // Called when a particle collides with another GameObject
    void OnParticleCollision(GameObject other)
    {
        if (playerController == null || riverParticleSystem == null) return;
        if (other != playerController.gameObject) return;
        // Reuse the preallocated particleBuffer to avoid allocations
        int particleCount = riverParticleSystem.particleCount;
        if (particleBuffer == null || particleBuffer.Length < particleCount)
            particleBuffer = new ParticleSystem.Particle[particleCount];
        int count = riverParticleSystem.GetParticles(particleBuffer);
        for (int i = 0; i < count; i++)
        {
            // Check if this particle is close to the player (collision is not per-particle, so we check distance)
            if ((particleBuffer[i].position - playerTransform.position).sqrMagnitude < 1.5f) // 1.5f is a small threshold
            {
                freedParticleSeeds.Add(particleBuffer[i].randomSeed);
            }
        }
    }

    // Add a struct for the job
    [BurstCompile]
    struct ParticleUpdateJob : IJobParallelFor
    {
        [ReadOnly] public NativeArray<Vector3> pathPoints;
        [ReadOnly] public NativeArray<float> pathZ;
        [ReadOnly] public Vector3 mainDirectionNormalized;
        [ReadOnly] public float riverRadius;
        [ReadOnly] public float flowSpeed;
        [ReadOnly] public float minYperParticle;
        [ReadOnly] public float maxYperParticle;
        [ReadOnly] public float cullDistanceStart;
        [ReadOnly] public float cullDistanceEnd;
        [ReadOnly] public Vector3 cameraPosition;
        [ReadOnly] public NativeArray<uint> freedParticleSeeds;
        [ReadOnly] public int pathPointsCount;

        public NativeArray<ParticleSystem.Particle> particles;

        // Binary search for segment index
        int BinarySearch(NativeArray<float> arr, float value)
        {
            int low = 0;
            int high = arr.Length - 1;
            while (low <= high)
            {
                int mid = (low + high) >> 1;
                float midVal = arr[mid];
                if (midVal < value)
                    low = mid + 1;
                else if (midVal > value)
                    high = mid - 1;
                else
                    return mid;
            }
            return ~low;
        }

        public void Execute(int i)
        {
            ParticleSystem.Particle particle = particles[i];
            float dist = Vector3.Distance(particle.position, cameraPosition);
            if (dist > cullDistanceEnd)
            {
                particle.remainingLifetime = -1f;
                particles[i] = particle;
                return;
            }
            // Skip steering for freed particles
            for (int j = 0; j < freedParticleSeeds.Length; j++)
            {
                if (particle.randomSeed == freedParticleSeeds[j])
                {
                    particles[i] = particle;
                    return;
                }
            }
            Vector3 P = particle.position;
            float distAlong = Vector3.Dot(P, mainDirectionNormalized);
            int index = BinarySearch(pathZ, distAlong);
            int segIndex;
            if (index >= 0)
                segIndex = index;
            else
            {
                int insertionPoint = ~index;
                if (insertionPoint == 0)
                    segIndex = 0;
                else if (insertionPoint == pathZ.Length)
                    segIndex = pathZ.Length - 2;
                else
                    segIndex = insertionPoint - 1;
            }
            if (segIndex < 0 || segIndex >= pathPointsCount - 1)
            {
                particles[i] = particle;
                return;
            }
            float z0 = pathZ[segIndex];
            float z1 = pathZ[segIndex + 1];
            float t = (P.z - z0) / (z1 - z0);
            Vector3 Q = Vector3.Lerp(pathPoints[segIndex], pathPoints[segIndex + 1], t);
            Vector3 T = (pathPoints[segIndex + 1] - pathPoints[segIndex]).normalized;
            Vector3 offset = P - Q;
            Vector3 offset_lateral = offset - Vector3.Dot(offset, T) * T;
            if (offset_lateral.magnitude > riverRadius)
            {
                Vector3 V_correction = -1.0f * offset_lateral;
                particle.velocity = flowSpeed * T + V_correction;
            }
            else
            {
                particle.velocity = flowSpeed * T;
            }
            Vector3 pos = particle.position;
            pos.y = Mathf.Clamp(pos.y, minYperParticle, maxYperParticle);
            particle.position = pos;
            if (dist > cullDistanceStart)
            {
                float fade = Mathf.InverseLerp(cullDistanceEnd, cullDistanceStart, dist);
                particle.remainingLifetime *= fade;
            }
            particles[i] = particle;
        }
    }

    void OnDestroy()
    {
        if (nativeParticles.IsCreated) nativeParticles.Dispose();
        if (nativePathPoints.IsCreated) nativePathPoints.Dispose();
        if (nativePathZ.IsCreated) nativePathZ.Dispose();
        if (nativeFreedSeeds.IsCreated) nativeFreedSeeds.Dispose();
    }

    void OnDisable()
    {
        if (nativeParticles.IsCreated) nativeParticles.Dispose();
        if (nativePathPoints.IsCreated) nativePathPoints.Dispose();
        if (nativePathZ.IsCreated) nativePathZ.Dispose();
        if (nativeFreedSeeds.IsCreated) nativeFreedSeeds.Dispose();
    }
}