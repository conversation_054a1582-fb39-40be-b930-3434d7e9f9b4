using UnityEngine;
using System.Collections.Generic;
using Random = UnityEngine.Random;

/// <summary>
/// Handles all movement-related behaviors for Soul Creatures using a weighted blending system.
/// Supports wandering, player mirroring, dolphin lunge, flocking, and orbiting behaviors.
/// </summary>
public class CreatureMovement : MonoBehaviour
{
    #region Core References and Boundary Settings

    // Core references
    private Transform playerTransform;
    private PlayerController playerController;

    // Boundary values for movement constraints
    private float minY = 0f;
    private float maxY = 10f;

    // Maximum deltaTime to prevent large jumps during FPS drops
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;

    [Header("Distance-Based Performance Settings")]
    [Tooltip("Distance from player where performance optimization starts")]
    [SerializeField] private float performanceOptimizationStartDistance = 2f;
    [Tooltip("Distance from player where maximum performance optimization is applied")]
    [SerializeField] private float performanceOptimizationMaxDistance = 25f;
    [Tooltip("Movement update interval for creatures close to player")]
    [SerializeField] private float minDistanceMovementUpdateInterval = 0.01f;
    [Tooltip("Movement update interval for creatures at maximum distance")]
    [SerializeField] private float maxDistanceMovementUpdateInterval = 1f;

    // Performance optimization variables
    private float movementUpdateTimer = 0f;
    private float currentMovementUpdateInterval = 0f; // 0 means update every frame
    private float accumulatedDeltaTime = 0f; // Accumulate deltaTime between updates

    #endregion

    #region Module Toggles

    [Header("Movement Module Toggles")]
    [Tooltip("Enable/disable weighted movement system")]
    public bool enableWeightedMovement = true;

    #endregion

    #region Weighted Movement System

    [Header("Weighted Movement Behaviors (0-1 range)")]
    [Tooltip("Weight for waypoint-based wandering behavior")]
    [Range(0f, 1f)] public float waypointWeight = 1f;

    [Tooltip("Weight for player movement mirroring behavior")]
    [Range(0f, 1f)] public float playerMirroringWeight = 0f;

    [Tooltip("Weight for direct attraction to player position")]
    [Range(0f, 1f)] public float playerAttractionWeight = 0f;

    [Tooltip("Weight for playful dolphin lunge behavior")]
    [Range(0f, 1f)] public float dolphinLungeWeight = 0f;

    [Tooltip("Weight for inter-creature flocking behavior")]
    [Range(0f, 1f)] public float flockingWeight = 0f;

    [Tooltip("Weight for player orbiting behavior")]
    [Range(0f, 1f)] public float orbitingWeight = 0f;

    // Internal weighted movement variables
    private Vector3 currentMovementDirection = Vector3.zero;

    // Speed magnetism variables
    private float baseWaypointWeight = 1f;
    private float basePlayerMirroringWeight = 0f;
    private float basePlayerAttractionWeight = 0f;
    private float baseDolphinLungeWeight = 0f;
    private float baseFlockingWeight = 0f;
    private float baseOrbitingWeight = 0f;
    private bool baseWeightsInitialized = false;

    // Personality variation multipliers (applied to base weights)
    private float personalityMirroringMultiplier = 1f;
    private float personalityAttractionMultiplier = 1f;
    private float personalityLungeMultiplier = 1f;
    private float personalityFlockingMultiplier = 1f;
    private float personalityOrbitingMultiplier = 1f;

    // Magnetism interest/cooldown system
    private bool isMagnetismActive = false;
    private float magnetismStartTime = 0f;
    private float magnetismInterestDuration = 0f; // Random 5-20 seconds
    private bool isInMagnetismCooldown = false;
    private float magnetismCooldownStartTime = 0f;
    private float magnetismCooldownDuration = 0f; // Random 2-10 seconds

    #endregion

    #region Dolphin Lunge Settings

    [Header("Dolphin Lunge Settings")]
    [Tooltip("Duration of dolphin lunge in seconds")]
    public float lungeDuration = 3f;

    [Tooltip("Cooldown between lunges in seconds")]
    public float lungeCooldown = 2f;

    [Tooltip("Minimum distance from player required to trigger lunge")]
    public float lungeMinDistance = 1f;

    [Tooltip("Maximum offset from exact player position for lunge target")]
    public float lungeTargetOffset = 0f;

    [Tooltip("Y-axis offset for lunge target (positive = above player, negative = below player)")]
    public float lungeTargetYOffset = -3f;

    [Tooltip("Speed multiplier for dolphin lunge")]
    public float lungeSpeedMultiplier = 300f;

    // Internal dolphin lunge variables
    private float lastLungeTime = -Mathf.Infinity;
    private bool isLunging = false;

    #endregion

    #region Player Mirroring Settings

    [Header("Player Mirroring Settings")]
    [Tooltip("Minimum mirroring strength")]
    public float mirroringStrengthMin = 16f;

    [Tooltip("Maximum mirroring strength")]
    public float mirroringStrengthMax = 23f;

    [Tooltip("Maximum distance from player for mirroring to occur")]
    public float mirroringMaxDistance = 10f;

    [Tooltip("Minimum time between mirroring strength changes")]
    public float mirroringChangeIntervalMin = 6f;

    [Tooltip("Maximum time between mirroring strength changes")]
    public float mirroringChangeIntervalMax = 12f;

    // Internal mirroring variables
    public float currentMirroringStrength = 5f;
    private float nextMirroringChangeTime = 0f;

    #endregion

    #region Flocking Settings

    [Header("Flocking Settings")]
    [Tooltip("Range for detecting nearby soul creatures")]
    public float flockingRange = 15f;

    [Tooltip("Weight for cohesion (moving toward group center)")]
    [Range(0f, 1f)] public float cohesionWeight = 0.33f;

    [Tooltip("Weight for separation (avoiding crowding)")]
    [Range(0f, 1f)] public float separationWeight = 0.33f;

    [Tooltip("Weight for alignment (matching neighbors' velocity)")]
    [Range(0f, 1f)] public float alignmentWeight = 0.34f;

    [Tooltip("Minimum distance to maintain from other creatures")]
    public float separationDistance = 5f;

    // Static caching for creature list optimization
    private static List<SoulCreatureLogic> cachedAllCreatures = new List<SoulCreatureLogic>();
    private static float lastCreatureListUpdateTime = -1f;
    private static readonly float creatureListUpdateInterval = 1f; // Update once per second

    #endregion

    #region Orbiting Settings

    [Header("Orbiting Settings")]
    [Tooltip("Radius of the imaginary sphere around player to orbit")]
    public float orbitRadius = 6.0f;

    [Tooltip("Speed of orbital movement (how fast to circle around the sphere)")]
    public float orbitSpeed = 3.0f;

    #endregion

    #region Wandering Settings

    [Header("Wandering Movement Settings")]
    [Tooltip("Minimum movement speed for wandering")]
    [SerializeField] private float minMoveSpeed = 1.5f;

    [Tooltip("Maximum movement speed for wandering")]
    [SerializeField] private float maxMoveSpeed = 3f;

    [Tooltip("Current randomized movement speed")]
    [SerializeField] private float moveSpeed = 2f;

    [Tooltip("Speed of rotation towards target")]
    [SerializeField] private float rotationSpeed = 50f;

    [Tooltip("Minimum distance to generate a new target point")]
    [SerializeField] private float minDistanceToTarget = 5f;

    [Tooltip("Maximum distance to generate a new target point")]
    [SerializeField] private float maxDistanceToTarget = 20f;

    [Tooltip("Minimum amplitude of the wave motion")]
    [SerializeField] private float minWaveAmplitude = 0.2f;

    [Tooltip("Maximum amplitude of the wave motion")]
    [SerializeField] private float maxWaveAmplitude = 0.8f;

    [Tooltip("Minimum length of the wave motion")]
    [SerializeField] private float minWaveLength = 2f;

    [Tooltip("Maximum length of the wave motion")]
    [SerializeField] private float maxWaveLength = 5f;

    [Tooltip("Maximum attempts to generate a valid target point")]
    [SerializeField] private int maxPointGenerationAttempts = 10;

    [Tooltip("How close to get before considering target reached")]
    [SerializeField] private float targetReachedThreshold = 1.5f;

    [Tooltip("Maximum time (seconds) to pursue the same target before generating a new one")]
    [SerializeField] private float maxTargetPursuitTime = 10f;

    [Tooltip("Speed for player attraction movement")]
    [SerializeField] private float attractionSpeed = 5f;

    // Wandering internal variables
    private Vector3 currentTargetPoint;
    private Queue<Vector3> customTargets = new Queue<Vector3>();
    private bool hasCustomTargets = false;
    private float currentWaveAmplitude;
    private float currentWaveLength;
    private float waveOffset;
    private float currentTargetPursuitTime = 0f;

    // Add this field to store a unique time offset per creature
    private float creatureStartTime;

    #endregion

    #region Unity Lifecycle Methods

    private void OnEnable()
    {
        // Reset movement state when object is reactivated from pool
        // This is good practice for pooled objects to ensure clean state
        creatureStartTime = Time.time + Random.value * 1000f; // Unique time base for wave motion
        ResetMovementState();
    }

    private void Start()
    {
        // Get references from GameManager
        if (GameManager.Instance != null)
        {
            GameManager gm = GameManager.Instance;
            playerTransform = gm.player.transform;
            playerController = gm.player;
            minY = gm.oceanBottom.transform.position.y - 2.0f;
            maxY = gm.cloudsBottom.transform.position.y + 2.0f;

            // Boundary values set successfully
        }
        else
        {
            Debug.LogError("GameManager instance not found!");
            enabled = false;
            return;
        }

        // Initialize movement settings
        InitializeMovementSettings();
    }

    private void Update()
    {
        if (playerTransform == null || playerController == null) return;

        // Calculate distance to player for performance optimization
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
        UpdateMovementPerformanceSettings(distanceToPlayer);

        // Always accumulate deltaTime
        accumulatedDeltaTime += Time.deltaTime;
        movementUpdateTimer += Time.deltaTime;

        // Check if we should update movement this frame
        if (currentMovementUpdateInterval > 0f && movementUpdateTimer < currentMovementUpdateInterval)
        {
            return; // Skip movement update this frame, but keep accumulating deltaTime
        }

        // Reset timers and use accumulated deltaTime for movement calculations
        movementUpdateTimer = 0f;
        float effectiveDeltaTime = accumulatedDeltaTime;
        accumulatedDeltaTime = 0f;

        // Update movement system with accumulated deltaTime
        if (enableWeightedMovement)
        {
            UpdateWeightedMovement(effectiveDeltaTime);
        }
    }

    #endregion

    #region Public Properties

    /// <summary>
    /// Current movement direction for flocking alignment behavior
    /// </summary>
    public Vector3 CurrentMovementDirection => currentMovementDirection;

    #endregion

    #region Object Pooling Reset

    /// <summary>
    /// Resets movement state when object is reactivated from pool.
    /// This ensures clean state for pooled objects and prevents issues from previous usage.
    /// </summary>
    private void ResetMovementState()
    {
        // Reset target and custom targets
        GenerateNewTargetPoint(); // Always generate a new random target on reset
        customTargets.Clear();
        hasCustomTargets = false;
        currentTargetPursuitTime = 0f;

        // Reset movement state
        currentMovementDirection = Vector3.zero;

        // Reset performance optimization timers
        movementUpdateTimer = 0f;
        accumulatedDeltaTime = 0f;

        // Reset dolphin lunge state
        isLunging = false;
        lastLungeTime = -Mathf.Infinity;

        // Reset mirroring state
        nextMirroringChangeTime = 0f;

        // Reset magnetism state
        isMagnetismActive = false;
        isInMagnetismCooldown = false;
        magnetismStartTime = 0f;
        magnetismCooldownStartTime = 0f;

        // Note: Don't log here as it would spam during pool reuse
    }

    #endregion

    #region Initialization

    private void InitializeMovementSettings()
    {
        // Randomize movement settings
        moveSpeed = Random.Range(minMoveSpeed, maxMoveSpeed);

        // Initialize wandering
        waveOffset = Random.Range(0f, 2f * Mathf.PI);
        currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
        currentWaveLength = Random.Range(minWaveLength, maxWaveLength);

        // Initialize base weights for speed magnetism
        InitializeBaseWeights();

        GenerateNewTargetPoint();
    }

    #endregion

    #region Performance Optimization

    private void UpdateMovementPerformanceSettings(float distanceToPlayer)
    {
        // Calculate performance optimization factor based on distance
        float optimizationFactor = 0f;
        if (distanceToPlayer > performanceOptimizationStartDistance)
        {
            optimizationFactor = Mathf.InverseLerp(
                performanceOptimizationStartDistance,
                performanceOptimizationMaxDistance,
                distanceToPlayer
            );
        }

        // Update movement update interval based on distance
        currentMovementUpdateInterval = Mathf.Lerp(
            minDistanceMovementUpdateInterval, // Update interval when close
            maxDistanceMovementUpdateInterval, // Update interval when far
            optimizationFactor
        );
    }

    #endregion

    #region Weighted Movement System Implementation

    private void UpdateWeightedMovement(float deltaTime = 0f)
    {
        // Use provided deltaTime or fall back to Time.deltaTime for backward compatibility
        float actualDeltaTime = deltaTime > 0f ? deltaTime : Time.deltaTime;

        // For accumulated deltaTime (from performance optimization), don't clamp it
        // Only clamp when using regular Time.deltaTime to prevent large jumps during FPS drops
        float clampedDeltaTime = deltaTime > 0f ? actualDeltaTime : Mathf.Min(actualDeltaTime, maxDeltaTime);
        Vector3 finalMovement = Vector3.zero;

        // Calculate each behavior's contribution
        if (waypointWeight > 0f)
        {
            finalMovement += CalculateWaypointMovement() * waypointWeight;
        }

        if (playerMirroringWeight > 0f)
        {
            finalMovement += CalculatePlayerMirroringMovement() * playerMirroringWeight;
        }

        if (playerAttractionWeight > 0f)
        {
            finalMovement += CalculatePlayerAttractionMovement() * playerAttractionWeight;
        }

        // Handle dolphin lunge separately to avoid speed clamping
        Vector3 dolphinLungeMovement = Vector3.zero;
        if (dolphinLungeWeight > 0f)
        {
            dolphinLungeMovement = CalculateDolphinLungeMovement() * dolphinLungeWeight;
        }

        if (flockingWeight > 0f)
        {
            finalMovement += CalculateFlockingMovement() * flockingWeight;
        }

        if (orbitingWeight > 0f)
        {
            finalMovement += CalculateOrbitingMovement() * orbitingWeight;
        }

        // Store current movement direction
        Vector3 totalMovement = finalMovement + dolphinLungeMovement;
        currentMovementDirection = totalMovement.normalized;

        // Apply the final movement
        if (totalMovement.sqrMagnitude > 0.0001f)
        {
            // Apply dolphin lunge movement directly without deltaTime multiplication to preserve speed
            if (dolphinLungeMovement.sqrMagnitude > 0.0001f)
            {
                transform.position += dolphinLungeMovement * clampedDeltaTime;
            }

            // Apply other movements normally
            if (finalMovement.sqrMagnitude > 0.0001f)
            {
                transform.position += finalMovement * clampedDeltaTime;
            }

            // Ensure we don't go past boundaries
            Vector3 clampedPosition = transform.position;
            clampedPosition.y = Mathf.Clamp(clampedPosition.y, minY, maxY);
            transform.position = clampedPosition;

            // Apply smooth rotation towards movement direction
            if (totalMovement.sqrMagnitude > 0.01f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(totalMovement.normalized);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * clampedDeltaTime);
            }
        }
    }

    private Vector3 CalculateWaypointMovement()
    {
        // Use existing wandering logic but return movement vector instead of applying directly
        UpdateWanderingTarget();

        Vector3 directionToTarget = (currentTargetPoint - transform.position).normalized;
        Vector3 movement = directionToTarget * moveSpeed;

        // Movement direction calculated successfully

        // Add wave motion for natural swimming - make it relative to movement direction instead of world space
        // Use a per-creature time base for wave motion
        float waveTime = (Time.time - creatureStartTime) / currentWaveLength;

        // Calculate perpendicular vectors to the movement direction for wave motion
        Vector3 rightVector = Vector3.Cross(directionToTarget, Vector3.up).normalized;
        Vector3 upVector = Vector3.Cross(rightVector, directionToTarget).normalized;

        // If movement is purely vertical, use world space vectors as fallback
        if (rightVector.sqrMagnitude < 0.1f)
        {
            rightVector = Vector3.right;
            upVector = Vector3.forward;
        }
        Vector3 upDownMotion = upVector * Mathf.Sin(waveTime * 2 * Mathf.PI + waveOffset) * currentWaveAmplitude;
        Vector3 leftRightMotion = rightVector * Mathf.Cos(waveTime * 2 * Mathf.PI + waveOffset + Mathf.PI / 4) * (currentWaveAmplitude * 0.7f);
        return movement + upDownMotion + leftRightMotion;
    }

    private Vector3 CalculatePlayerMirroringMovement()
    {
        // Check distance to player first
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
        if (distanceToPlayer > mirroringMaxDistance)
        {
            return Vector3.zero;
        }

        // Check if player is moving
        Vector3 playerMoveDir = playerController.lastMoveDirection3D;
        if (playerMoveDir.sqrMagnitude <= 0.0001f) return Vector3.zero;

        // Update mirroring strength based on timing
        UpdateMirroringStrength();

        return playerMoveDir.normalized * currentMirroringStrength;
    }

    private void UpdateMirroringStrength()
    {
        float currentTime = Time.time;

        // Check if it's time to change the mirroring strength
        if (currentTime >= nextMirroringChangeTime)
        {
            // Set new mirroring strength
            currentMirroringStrength = Random.Range(mirroringStrengthMin, mirroringStrengthMax);

            // Schedule next change
            float nextInterval = Random.Range(mirroringChangeIntervalMin, mirroringChangeIntervalMax);
            nextMirroringChangeTime = currentTime + nextInterval;
        }
    }

    private Vector3 CalculatePlayerAttractionMovement()
    {
        Vector3 attractionDirection = (playerTransform.position - transform.position).normalized;
        return attractionDirection * attractionSpeed;
    }

    private Vector3 CalculateDolphinLungeMovement()
    {
        float currentTime = Time.time;

        // Check distance to player
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);

        // Only lunge if player is far enough away
        if (distanceToPlayer < lungeMinDistance)
        {
            return Vector3.zero;
        }

        // Check if we should start a new lunge
        if (!isLunging && currentTime - lastLungeTime >= lungeCooldown)
        {
            // Start new lunge
            isLunging = true;
            lastLungeTime = currentTime;
        }

        // Execute lunge if active
        if (isLunging)
        {
            if (currentTime - lastLungeTime < lungeDuration)
            {
                // Always target the player's current position with optional offset
                Vector3 offset = Random.insideUnitSphere * lungeTargetOffset;
                offset.y *= 0.5f; // Reduce random vertical offset
                Vector3 currentLungeTarget = playerTransform.position + offset;

                // Apply the Y offset setting
                currentLungeTarget.y += lungeTargetYOffset;

                // Ensure target is within boundaries
                currentLungeTarget.y = Mathf.Clamp(currentLungeTarget.y, minY, maxY);

                Vector3 lungeDirection = (currentLungeTarget - transform.position).normalized;
                return lungeDirection * moveSpeed * lungeSpeedMultiplier;
            }
            else
            {
                // End lunge
                isLunging = false;
            }
        }

        return Vector3.zero;
    }

    private Vector3 CalculateFlockingMovement()
    {
        Vector3 cohesion = Vector3.zero;
        Vector3 separation = Vector3.zero;
        Vector3 alignment = Vector3.zero;
        int neighborCount = 0;

        // Update cached creature list only once per second
        UpdateCachedCreatureList();

        foreach (var creature in cachedAllCreatures)
        {
            if (creature == null || creature.transform == transform) continue;

            float distance = Vector3.Distance(transform.position, creature.transform.position);
            if (distance <= flockingRange)
            {
                neighborCount++;

                // Cohesion: move toward group center
                cohesion += creature.transform.position;

                // Separation: avoid crowding
                if (distance < separationDistance && distance > 0.1f)
                {
                    Vector3 separationForce = (transform.position - creature.transform.position).normalized / distance;
                    separation += separationForce;
                }

                // Alignment: match neighbors' velocity
                // Get movement direction from creature's CreatureMovement component
                CreatureMovement creatureMovement = creature.GetComponent<CreatureMovement>();
                if (creatureMovement != null)
                {
                    alignment += creatureMovement.CurrentMovementDirection;
                }
            }
        }

        Vector3 flockingForce = Vector3.zero;

        if (neighborCount > 0)
        {
            // Cohesion
            cohesion = (cohesion / neighborCount - transform.position).normalized * moveSpeed * cohesionWeight;

            // Alignment
            alignment = (alignment / neighborCount).normalized * moveSpeed * alignmentWeight;

            // Combine forces
            flockingForce = cohesion + alignment;
        }

        // Add separation (calculated separately to avoid division issues)
        flockingForce += separation.normalized * moveSpeed * separationWeight;

        return flockingForce;
    }

    /// <summary>
    /// Updates the cached creature list only once per second to optimize performance
    /// </summary>
    private static void UpdateCachedCreatureList()
    {
        float currentTime = Time.time;
        if (currentTime - lastCreatureListUpdateTime >= creatureListUpdateInterval)
        {
            cachedAllCreatures.Clear();
            cachedAllCreatures.AddRange(SoulCreatureLogic.GetAllActiveInstances());
            lastCreatureListUpdateTime = currentTime;
        }
    }

    private Vector3 CalculateOrbitingMovement()
    {
        // Get the vector from player to creature
        Vector3 toCreature = transform.position - playerTransform.position;
        float currentDistance = toCreature.magnitude;

        // Handle edge case where creature is too close to player
        if (currentDistance < 0.1f)
        {
            // Move in a random direction to get away from the singularity
            return Vector3.up * moveSpeed;
        }

        // Step 1: Calculate radial correction to maintain orbit radius
        float radiusError = currentDistance - orbitRadius;
        Vector3 radialCorrection = -toCreature.normalized * radiusError * moveSpeed * 0.3f;

        // Step 2: Calculate tangential velocity for orbital motion
        // The tangent to a sphere at any point is perpendicular to the radius vector
        // We can get a tangent by taking the cross product of the radius with any non-parallel vector

        // Choose a reference vector that's not parallel to toCreature
        Vector3 referenceVector = Vector3.up;
        if (Mathf.Abs(Vector3.Dot(toCreature.normalized, Vector3.up)) > 0.9f)
        {
            // If toCreature is nearly parallel to up, use right instead
            referenceVector = Vector3.right;
        }

        // Calculate the tangent vector (perpendicular to radius)
        Vector3 tangent = Vector3.Cross(toCreature.normalized, referenceVector).normalized;

        // The orbital velocity is along this tangent
        Vector3 orbitalVelocity = tangent * orbitSpeed * moveSpeed;

        // Step 3: Combine radial correction with orbital motion
        Vector3 totalMovement = radialCorrection + orbitalVelocity;

        // Step 4: Ensure movement stays within Y boundaries
        Vector3 futurePosition = transform.position + totalMovement * Time.deltaTime;
        if (futurePosition.y < minY || futurePosition.y > maxY)
        {
            // Clamp the Y component to stay within bounds
            totalMovement.y = Mathf.Clamp(totalMovement.y,
                (minY - transform.position.y) / Time.deltaTime,
                (maxY - transform.position.y) / Time.deltaTime);
        }

        return totalMovement;
    }



    #endregion

    #region Wandering Implementation

    private void UpdateWanderingTarget()
    {
        // Update the target pursuit timer
        currentTargetPursuitTime += Time.deltaTime;

        // Check if we've been pursuing the same target for too long
        if (currentTargetPursuitTime > maxTargetPursuitTime)
        {
            if (hasCustomTargets)
            {
                customTargets.Clear();
                hasCustomTargets = false;
            }
            GenerateNewTargetPoint();
            currentTargetPursuitTime = 0f;
        }

        // Check if we should use a custom target point
        if (customTargets.Count > 0 && !hasCustomTargets)
        {
            currentTargetPoint = customTargets.Peek();
            hasCustomTargets = true;
            currentTargetPursuitTime = 0f;
        }

        // Check if we've reached the target
        float distanceToTarget = Vector3.Distance(transform.position, currentTargetPoint);
        if (distanceToTarget < targetReachedThreshold)
        {
            if (hasCustomTargets)
            {
                customTargets.Dequeue();
                if (customTargets.Count == 0)
                {
                    hasCustomTargets = false;
                    GenerateNewTargetPoint();
                }
                else
                {
                    currentTargetPoint = customTargets.Peek();
                }
            }
            else
            {
                GenerateNewTargetPoint();
                currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
                currentWaveLength = Random.Range(minWaveLength, maxWaveLength);
                waveOffset = Random.Range(0f, 2f * Mathf.PI);
            }
        }
    }
    private void GenerateNewTargetPoint()
    {
        // Generate a truly random target point within a sphere around the creature
        // that respects Y boundaries without creating directional bias

        Vector3 potentialTarget;
        int attempts = 0;
        const int maxAttempts = 20;

        do
        {
            attempts++;

            // Choose a random distance within our range
            float minDistance = Mathf.Max(minDistanceToTarget, targetReachedThreshold);
            float targetDistance = Random.Range(minDistance, maxDistanceToTarget);

            // Generate a truly random point on a sphere using uniform distribution
            // This avoids the bias that comes from Euler angles
            Vector3 randomDirection = Random.insideUnitSphere.normalized;

            // Calculate potential target
            potentialTarget = transform.position + randomDirection * targetDistance;

            // Check if target is within Y boundaries
            if (potentialTarget.y >= minY && potentialTarget.y <= maxY)
            {
                break; // Valid target found
            }

            // If we're running out of attempts, just clamp Y and accept it
            if (attempts >= maxAttempts)
            {
                potentialTarget.y = Mathf.Clamp(potentialTarget.y, minY, maxY);
                break;
            }

        } while (attempts < maxAttempts);

        // Target generation completed successfully

        // Assign the new target
        currentTargetPoint = potentialTarget;
    }

    #endregion

    #region Speed Magnetism System

    /// <summary>
    /// Initializes base weights for speed magnetism calculations
    /// </summary>
    private void InitializeBaseWeights()
    {
        if (!baseWeightsInitialized)
        {
            baseWaypointWeight = waypointWeight;
            basePlayerMirroringWeight = playerMirroringWeight;
            basePlayerAttractionWeight = playerAttractionWeight;
            baseDolphinLungeWeight = dolphinLungeWeight;
            baseFlockingWeight = flockingWeight;
            baseOrbitingWeight = orbitingWeight;

            // Initialize personality multipliers (X% less to X% more than base values)
            personalityMirroringMultiplier = Random.Range(0.5f, 1.2f);
            personalityAttractionMultiplier = Random.Range(0.5f, 1.2f);
            personalityLungeMultiplier = Random.Range(0.5f, 1.2f);
            personalityFlockingMultiplier = Random.Range(0.5f, 1.2f);
            personalityOrbitingMultiplier = Random.Range(0.5f, 1.2f);

            // Apply personality to base weights (except waypoint which stays as is)
            basePlayerMirroringWeight = Mathf.Clamp01(basePlayerMirroringWeight * personalityMirroringMultiplier);
            basePlayerAttractionWeight = Mathf.Clamp01(basePlayerAttractionWeight * personalityAttractionMultiplier);
            baseDolphinLungeWeight = Mathf.Clamp01(baseDolphinLungeWeight * personalityLungeMultiplier);
            baseFlockingWeight = Mathf.Clamp01(baseFlockingWeight * personalityFlockingMultiplier);
            baseOrbitingWeight = Mathf.Clamp01(baseOrbitingWeight * personalityOrbitingMultiplier);

            baseWeightsInitialized = true;
        }
    }

    /// <summary>
    /// Applies speed magnetism effects to movement weights based on player speed and distance
    /// </summary>
    /// <param name="magnetismStrength">Combined speed and distance factor (0-1)</param>
    /// <param name="waypointReductionMultiplier">How much to reduce waypoint weight (0-1)</param>
    /// <param name="mirroringMultiplier">Multiplier for mirroring weight</param>
    /// <param name="attractionMultiplier">Multiplier for attraction weight</param>
    /// <param name="lungeMultiplier">Multiplier for lunge weight</param>
    /// <param name="flockingMultiplier">Multiplier for flocking weight</param>
    /// <param name="orbitingMultiplier">Multiplier for orbiting weight</param>
    public void ApplySpeedMagnetism(float magnetismStrength, float waypointReductionMultiplier, float mirroringMultiplier,
        float attractionMultiplier, float lungeMultiplier, float flockingMultiplier, float orbitingMultiplier)
    {
        // Initialize base weights if not done yet
        InitializeBaseWeights();

        // Check if creature is in cooldown period
        if (isInMagnetismCooldown)
        {
            float currentTime = Time.time;
            if (currentTime - magnetismCooldownStartTime >= magnetismCooldownDuration)
            {
                // Cooldown finished, can be magnetized again
                isInMagnetismCooldown = false;
            }
            else
            {
                // Still in cooldown, ignore magnetism
                return;
            }
        }

        // Check if magnetism should start or continue
        if (magnetismStrength > 0.01f)
        {
            if (!isMagnetismActive)
            {
                // Start new magnetism session
                isMagnetismActive = true;
                magnetismStartTime = Time.time;
                magnetismInterestDuration = Random.Range(5f, 20f); // Random interest duration
            }
            else
            {
                // Check if interest duration has expired
                float currentTime = Time.time;
                if (currentTime - magnetismStartTime >= magnetismInterestDuration)
                {
                    // Lost interest, start cooldown
                    isMagnetismActive = false;
                    isInMagnetismCooldown = true;
                    magnetismCooldownStartTime = currentTime;
                    magnetismCooldownDuration = Random.Range(2f, 10f); // Random cooldown duration

                    // Reset to base weights and exit
                    ResetToBaseWeights();
                    return;
                }
            }
        }
        else
        {
            // No magnetism strength, reset active state
            isMagnetismActive = false;
        }

        // Apply magnetism effects to each weight
        // The magnetism system adds weight based on magnetism strength, regardless of base weight
        // This allows magnetism to work even when base weights are 0

        // Reduce waypoint weight based on magnetism strength
        float waypointReduction = waypointReductionMultiplier * magnetismStrength;
        waypointWeight = Mathf.Clamp01(baseWaypointWeight * (1f - waypointReduction));

        // Increase following behavior weights
        float mirroringBoost = mirroringMultiplier * magnetismStrength * 0.2f; // Scale down to reasonable range
        float attractionBoost = attractionMultiplier * magnetismStrength * 0.2f;
        float lungeBoost = lungeMultiplier * magnetismStrength * 0.2f;
        float flockingBoost = flockingMultiplier * magnetismStrength * 0.2f;
        float orbitingBoost = orbitingMultiplier * magnetismStrength * 0.2f;

        playerMirroringWeight = Mathf.Clamp01(basePlayerMirroringWeight + mirroringBoost);
        playerAttractionWeight = Mathf.Clamp01(basePlayerAttractionWeight + attractionBoost);
        dolphinLungeWeight = Mathf.Clamp01(baseDolphinLungeWeight + lungeBoost);
        flockingWeight = Mathf.Clamp01(baseFlockingWeight + flockingBoost);
        orbitingWeight = Mathf.Clamp01(baseOrbitingWeight + orbitingBoost);
    }

    /// <summary>
    /// Resets movement weights to their base values (removes magnetism effects)
    /// </summary>
    public void ResetToBaseWeights()
    {
        if (baseWeightsInitialized)
        {
            waypointWeight = baseWaypointWeight;
            playerMirroringWeight = basePlayerMirroringWeight;
            playerAttractionWeight = basePlayerAttractionWeight;
            dolphinLungeWeight = baseDolphinLungeWeight;
            flockingWeight = baseFlockingWeight;
            orbitingWeight = baseOrbitingWeight;
        }
    }

    #endregion
}