{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 57575, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 57575, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 57575, "tid": 1485895, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486109837, "dur": 604, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486115392, "dur": 961, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 57575, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 57575, "tid": 1, "ts": 1748632485176749, "dur": 15355, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 57575, "tid": 1, "ts": 1748632485192109, "dur": 122149, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 57575, "tid": 1, "ts": 1748632485314265, "dur": 69861, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486116359, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 57575, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485174639, "dur": 4560, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485179201, "dur": 921196, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485180368, "dur": 4108, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485184503, "dur": 2616, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485187121, "dur": 19214, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485206349, "dur": 544, "ph": "X", "name": "ProcessMessages 106", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485206895, "dur": 38, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485206934, "dur": 6, "ph": "X", "name": "ProcessMessages 8168", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485206941, "dur": 19, "ph": "X", "name": "ReadAsync 8168", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485206966, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485206968, "dur": 184, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207153, "dur": 3, "ph": "X", "name": "ProcessMessages 4495", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207178, "dur": 187, "ph": "X", "name": "ReadAsync 4495", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207371, "dur": 5, "ph": "X", "name": "ProcessMessages 6661", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207377, "dur": 21, "ph": "X", "name": "ReadAsync 6661", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207551, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207553, "dur": 29, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207583, "dur": 3, "ph": "X", "name": "ProcessMessages 5103", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207588, "dur": 34, "ph": "X", "name": "ReadAsync 5103", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207627, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207629, "dur": 30, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207661, "dur": 1, "ph": "X", "name": "ProcessMessages 1213", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207662, "dur": 31, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207695, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207696, "dur": 20, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207719, "dur": 26, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207749, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207751, "dur": 34, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207785, "dur": 1, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207787, "dur": 46, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207834, "dur": 1, "ph": "X", "name": "ProcessMessages 1650", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485207836, "dur": 331, "ph": "X", "name": "ReadAsync 1650", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485208169, "dur": 4, "ph": "X", "name": "ProcessMessages 5730", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485208176, "dur": 19, "ph": "X", "name": "ReadAsync 5730", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485208197, "dur": 34, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485208234, "dur": 200, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485208439, "dur": 560, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209001, "dur": 2, "ph": "X", "name": "ProcessMessages 2498", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209004, "dur": 22, "ph": "X", "name": "ReadAsync 2498", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209028, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209033, "dur": 482, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209538, "dur": 20, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209560, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209562, "dur": 48, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209612, "dur": 153, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209767, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209768, "dur": 88, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209857, "dur": 1, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485209859, "dur": 229, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210089, "dur": 2, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210092, "dur": 139, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210234, "dur": 216, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210451, "dur": 2, "ph": "X", "name": "ProcessMessages 1936", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210454, "dur": 56, "ph": "X", "name": "ReadAsync 1936", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210511, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210512, "dur": 63, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210577, "dur": 1, "ph": "X", "name": "ProcessMessages 1247", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210578, "dur": 56, "ph": "X", "name": "ReadAsync 1247", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210635, "dur": 1, "ph": "X", "name": "ProcessMessages 1736", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210638, "dur": 32, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210673, "dur": 23, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485210698, "dur": 347, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211047, "dur": 2, "ph": "X", "name": "ProcessMessages 2788", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211050, "dur": 21, "ph": "X", "name": "ReadAsync 2788", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211073, "dur": 19, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211092, "dur": 18, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211111, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211113, "dur": 287, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211407, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211409, "dur": 30, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211440, "dur": 1, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211442, "dur": 34, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211478, "dur": 90, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211569, "dur": 1, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211571, "dur": 57, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211630, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211632, "dur": 102, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485211736, "dur": 359, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212097, "dur": 3, "ph": "X", "name": "ProcessMessages 4133", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212101, "dur": 31, "ph": "X", "name": "ReadAsync 4133", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212133, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212135, "dur": 21, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212159, "dur": 241, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212401, "dur": 2, "ph": "X", "name": "ProcessMessages 2080", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212404, "dur": 250, "ph": "X", "name": "ReadAsync 2080", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212655, "dur": 1, "ph": "X", "name": "ProcessMessages 1551", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212657, "dur": 40, "ph": "X", "name": "ReadAsync 1551", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212699, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485212700, "dur": 725, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485213426, "dur": 4, "ph": "X", "name": "ProcessMessages 4120", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485213431, "dur": 592, "ph": "X", "name": "ReadAsync 4120", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214025, "dur": 2, "ph": "X", "name": "ProcessMessages 3450", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214028, "dur": 22, "ph": "X", "name": "ReadAsync 3450", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214051, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214052, "dur": 21, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214084, "dur": 36, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214122, "dur": 1, "ph": "X", "name": "ProcessMessages 1460", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214124, "dur": 166, "ph": "X", "name": "ReadAsync 1460", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214290, "dur": 2, "ph": "X", "name": "ProcessMessages 3148", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214293, "dur": 82, "ph": "X", "name": "ReadAsync 3148", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214378, "dur": 19, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214399, "dur": 26, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214427, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214428, "dur": 218, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214648, "dur": 1, "ph": "X", "name": "ProcessMessages 1386", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214649, "dur": 19, "ph": "X", "name": "ReadAsync 1386", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214671, "dur": 33, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214706, "dur": 42, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214749, "dur": 1, "ph": "X", "name": "ProcessMessages 1319", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214751, "dur": 24, "ph": "X", "name": "ReadAsync 1319", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214776, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214778, "dur": 68, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485214848, "dur": 291, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215140, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215142, "dur": 19, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215162, "dur": 2, "ph": "X", "name": "ProcessMessages 3201", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215165, "dur": 79, "ph": "X", "name": "ReadAsync 3201", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215246, "dur": 1, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215248, "dur": 16, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215266, "dur": 35, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215304, "dur": 273, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215578, "dur": 3, "ph": "X", "name": "ProcessMessages 4526", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215583, "dur": 20, "ph": "X", "name": "ReadAsync 4526", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215605, "dur": 14, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215622, "dur": 43, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215668, "dur": 59, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215728, "dur": 1, "ph": "X", "name": "ProcessMessages 1429", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215730, "dur": 26, "ph": "X", "name": "ReadAsync 1429", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215759, "dur": 141, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215902, "dur": 32, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215936, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215938, "dur": 17, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215958, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485215983, "dur": 20, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216008, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216010, "dur": 23, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216035, "dur": 24, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216261, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216262, "dur": 28, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216291, "dur": 3, "ph": "X", "name": "ProcessMessages 5060", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216303, "dur": 105, "ph": "X", "name": "ReadAsync 5060", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216499, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216500, "dur": 322, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216825, "dur": 2, "ph": "X", "name": "ProcessMessages 3215", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485216834, "dur": 354, "ph": "X", "name": "ReadAsync 3215", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217190, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217191, "dur": 19, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217211, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217213, "dur": 30, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217245, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217270, "dur": 15, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217288, "dur": 19, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217334, "dur": 343, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217678, "dur": 1, "ph": "X", "name": "ProcessMessages 1290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217690, "dur": 22, "ph": "X", "name": "ReadAsync 1290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485217713, "dur": 2, "ph": "X", "name": "ProcessMessages 2708", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218006, "dur": 31, "ph": "X", "name": "ReadAsync 2708", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218039, "dur": 5, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218046, "dur": 25, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218072, "dur": 1, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218074, "dur": 32, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218107, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218113, "dur": 15, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218131, "dur": 347, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218480, "dur": 6, "ph": "X", "name": "ProcessMessages 8179", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485218486, "dur": 637, "ph": "X", "name": "ReadAsync 8179", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485219125, "dur": 4, "ph": "X", "name": "ProcessMessages 5673", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485219130, "dur": 880, "ph": "X", "name": "ReadAsync 5673", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485220142, "dur": 4, "ph": "X", "name": "ProcessMessages 4634", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485220147, "dur": 33, "ph": "X", "name": "ReadAsync 4634", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485220311, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485220313, "dur": 30, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485220356, "dur": 484, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485220843, "dur": 402, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485221248, "dur": 222, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485221473, "dur": 167, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485221642, "dur": 814, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485222459, "dur": 46, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485222507, "dur": 521, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485223032, "dur": 71, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485223106, "dur": 1066, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485224178, "dur": 317, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485224497, "dur": 85, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485224608, "dur": 116, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485224731, "dur": 677, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485225410, "dur": 1427, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485226839, "dur": 1, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485226841, "dur": 654, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485227507, "dur": 2, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485227510, "dur": 1099, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485228613, "dur": 1, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485228615, "dur": 110, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485228726, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485228728, "dur": 672, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485229403, "dur": 335, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485229796, "dur": 136, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485229935, "dur": 193, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485230137, "dur": 607, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485230745, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485230747, "dur": 9849, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485240620, "dur": 7, "ph": "X", "name": "ProcessMessages 8179", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485241851, "dur": 52, "ph": "X", "name": "ReadAsync 8179", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485241905, "dur": 2, "ph": "X", "name": "ProcessMessages 2402", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485241918, "dur": 395, "ph": "X", "name": "ReadAsync 2402", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485242316, "dur": 420, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485242738, "dur": 2, "ph": "X", "name": "ProcessMessages 2198", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485242741, "dur": 726, "ph": "X", "name": "ReadAsync 2198", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485243467, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485243469, "dur": 778, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485244250, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485244251, "dur": 136, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485244391, "dur": 1265, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485248049, "dur": 306, "ph": "X", "name": "ProcessMessages 2724", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485248356, "dur": 45, "ph": "X", "name": "ReadAsync 2724", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485248403, "dur": 3, "ph": "X", "name": "ProcessMessages 3398", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485248410, "dur": 418, "ph": "X", "name": "ReadAsync 3398", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485248833, "dur": 31, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485248876, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485248896, "dur": 196, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485249093, "dur": 476, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485249572, "dur": 9780, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485259356, "dur": 7, "ph": "X", "name": "ProcessMessages 8138", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485259364, "dur": 194, "ph": "X", "name": "ReadAsync 8138", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485259560, "dur": 626, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485279043, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485279047, "dur": 76, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485279124, "dur": 6, "ph": "X", "name": "ProcessMessages 8170", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485279140, "dur": 37, "ph": "X", "name": "ReadAsync 8170", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485279201, "dur": 35966, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315170, "dur": 8, "ph": "X", "name": "ProcessMessages 8188", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315179, "dur": 68, "ph": "X", "name": "ReadAsync 8188", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315250, "dur": 303, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315555, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315557, "dur": 187, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315747, "dur": 23, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315771, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315773, "dur": 25, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315799, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485315801, "dur": 1104, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485316907, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485316908, "dur": 69, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485316981, "dur": 15, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485316998, "dur": 40, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317039, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317041, "dur": 31, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317075, "dur": 537, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317614, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317616, "dur": 21, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317641, "dur": 42, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317684, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485317686, "dur": 1115, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485318804, "dur": 228, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485319034, "dur": 149, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485319194, "dur": 283, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485319479, "dur": 629, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320110, "dur": 5, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320116, "dur": 96, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320252, "dur": 357, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320610, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320619, "dur": 216, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320838, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320866, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320868, "dur": 64, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485320935, "dur": 175, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321111, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321115, "dur": 53, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321171, "dur": 65, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321446, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321448, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321482, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321483, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321526, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321812, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321814, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485321852, "dur": 351, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322206, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322271, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322273, "dur": 111, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322385, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322386, "dur": 115, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322504, "dur": 338, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322844, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485322846, "dur": 275, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485323122, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485323124, "dur": 117, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485323385, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485323387, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485323411, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485323414, "dur": 2494, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485325911, "dur": 18, "ph": "X", "name": "ProcessMessages 2992", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485325959, "dur": 370, "ph": "X", "name": "ReadAsync 2992", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485326331, "dur": 4443, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485330777, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485330798, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485330852, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485330905, "dur": 2089, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485332997, "dur": 6162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485339191, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485339408, "dur": 826, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485340236, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485340238, "dur": 877, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485341117, "dur": 558, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485341678, "dur": 395, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485342076, "dur": 412, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485342516, "dur": 248, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485342767, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485342887, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485343196, "dur": 502, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485343713, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485343720, "dur": 263, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485343984, "dur": 43, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485344029, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485344158, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485344183, "dur": 400, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485344585, "dur": 429, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485345017, "dur": 607, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485345627, "dur": 415, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485346045, "dur": 637, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485346684, "dur": 637, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485347324, "dur": 723, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485348054, "dur": 665, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485348720, "dur": 14, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485348743, "dur": 749, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485349494, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485349496, "dur": 951, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485350449, "dur": 1769, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485352220, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485352382, "dur": 942, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485353332, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485353448, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485353583, "dur": 3167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485356752, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485356754, "dur": 117, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485356874, "dur": 338, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357214, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357374, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357455, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357519, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357521, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357556, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357559, "dur": 58, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485357619, "dur": 557, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485358202, "dur": 824, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485361466, "dur": 70433, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485431906, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485431912, "dur": 91, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485432005, "dur": 9, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485432014, "dur": 35370, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485467390, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485467393, "dur": 117, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485467517, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485467522, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485468256, "dur": 29, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485468288, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485468320, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485468365, "dur": 45, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485468411, "dur": 2066, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485470480, "dur": 2012, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485472495, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485472498, "dur": 538, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485473040, "dur": 600, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485473643, "dur": 968, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485474614, "dur": 241, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485474858, "dur": 1509, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485476371, "dur": 487, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485476861, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485476981, "dur": 870, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485477854, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485478056, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485478271, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485478275, "dur": 1241, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485479518, "dur": 228, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485479749, "dur": 1588, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485481340, "dur": 174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485481517, "dur": 1445, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485482966, "dur": 324, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485483292, "dur": 1545, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485484840, "dur": 255, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485485107, "dur": 835, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485485945, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485486004, "dur": 622, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485486630, "dur": 415, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485487048, "dur": 340, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485487390, "dur": 1000, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485488397, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485488401, "dur": 342, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485488746, "dur": 850, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485489599, "dur": 421, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485490022, "dur": 268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485490296, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485490300, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485490525, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485490624, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485490627, "dur": 353, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485490983, "dur": 1318, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485492303, "dur": 355, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485492660, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485492747, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485492858, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485492887, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485492913, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493009, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493013, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493068, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493108, "dur": 515, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493624, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493626, "dur": 81, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493716, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493834, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493863, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493946, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485493967, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494045, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494076, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494196, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494257, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494286, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494313, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494439, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494486, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494530, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494623, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494703, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494737, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494797, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494857, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494882, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494902, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494931, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485494991, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495058, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495082, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495147, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495170, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495191, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495226, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495281, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495351, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495353, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495429, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495594, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495725, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495752, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495832, "dur": 10, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495845, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485495977, "dur": 480, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485496459, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485496561, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485496563, "dur": 332752, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485829321, "dur": 33, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485829355, "dur": 1221, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485830579, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485830582, "dur": 1567, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485832151, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485832153, "dur": 130861, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485963025, "dur": 15, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485963049, "dur": 2647, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485965699, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632485965702, "dur": 120849, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486086558, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486086561, "dur": 62, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486086625, "dur": 103, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486086731, "dur": 101, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486086835, "dur": 95, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486086932, "dur": 29, "ph": "X", "name": "ProcessMessages 7973", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486086962, "dur": 3133, "ph": "X", "name": "ReadAsync 7973", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486090100, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486090104, "dur": 584, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486090691, "dur": 31, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486090731, "dur": 874, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486091608, "dur": 89, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486091700, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486091770, "dur": 50, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486091821, "dur": 118, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486091941, "dur": 11, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486091953, "dur": 2616, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486094571, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486094574, "dur": 271, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486094847, "dur": 24, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486094872, "dur": 171, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486095045, "dur": 276, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748632486095323, "dur": 5034, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486116374, "dur": 983, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 57575, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 57575, "tid": 8589934592, "ts": 1748632485170794, "dur": 213358, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 57575, "tid": 8589934592, "ts": 1748632485384154, "dur": 17, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 57575, "tid": 8589934592, "ts": 1748632485384172, "dur": 1994, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486117359, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 57575, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 57575, "tid": 4294967296, "ts": 1748632485069689, "dur": 1032621, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748632485076150, "dur": 90997, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748632486102375, "dur": 4085, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748632486105193, "dur": 56, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748632486106529, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486117368, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748632485176692, "dur": 3148, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748632485179851, "dur": 27027, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748632485206934, "dur": 145, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748632485207079, "dur": 87, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748632485207680, "dur": 235, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748632485218855, "dur": 156, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748632485219390, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748632485236231, "dur": 5215, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748632485257608, "dur": 2726, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748632485265676, "dur": 14412, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748632485283257, "dur": 32881, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748632485207171, "dur": 112575, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748632485319753, "dur": 776022, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748632486095923, "dur": 1147, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748632485207128, "dur": 112633, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485319765, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748632485319942, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485320398, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485320501, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485320697, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485320797, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485320854, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485320937, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485321034, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485321144, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485321231, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485321370, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485321486, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485321548, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485321624, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485321741, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485321818, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485321880, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485322230, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748632485322900, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485323756, "dur": 552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485324347, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748632485324777, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748632485325064, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748632485325779, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748632485325874, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485326056, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748632485326694, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485327533, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485328215, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485328870, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485329548, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485330226, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485330866, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485331490, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485332212, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485332872, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485333662, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485334317, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485334994, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485335614, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485336217, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485336846, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485337512, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485338151, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485339049, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485339746, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485340536, "dur": 1473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485342042, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485344740, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485344828, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485345855, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485346981, "dur": 3144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485350126, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485350442, "dur": 2675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485353117, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485353280, "dur": 3165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485356445, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485356615, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485357157, "dur": 15708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485372866, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485373020, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_4EC1FE8B3807FE28.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485373129, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485373207, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485374710, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485374773, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485375013, "dur": 1292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485376307, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485376563, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748632485376615, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485377157, "dur": 1269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485378426, "dur": 92018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485470445, "dur": 5165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485475641, "dur": 3338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485479032, "dur": 3143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485482175, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485482317, "dur": 2073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485484402, "dur": 1687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485486110, "dur": 1850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485488010, "dur": 2508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485490519, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485490592, "dur": 2921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485495301, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485495473, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485496472, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485496537, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485496897, "dur": 334020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485831085, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748632485830918, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632485832820, "dur": 200, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632486092474, "dur": 346, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748632485833341, "dur": 259484, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748632486095407, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748632486095404, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748632486095478, "dur": 240, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748632485207129, "dur": 112639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485319778, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748632485319941, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485320431, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485320585, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485320705, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485320800, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485320863, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485320951, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485321069, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485321230, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485321320, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485321394, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485321542, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485321677, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485321758, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485321897, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485322003, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485322313, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485322988, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485323164, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748632485323401, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485323826, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748632485323942, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485324048, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485324115, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485324218, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485324781, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485324915, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485325347, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748632485325713, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485325879, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485325934, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485326144, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485326275, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748632485326619, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485326732, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485327578, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485328264, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485328923, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485329601, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485330275, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485330923, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485331558, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485332270, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485332961, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485333865, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485334584, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485335221, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485335844, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485336431, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485337070, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485337781, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485338461, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485339346, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485340018, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485340682, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485340894, "dur": 1457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485342351, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485342607, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_B533E0F362656246.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485342694, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485343998, "dur": 2231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485346230, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485346536, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485347607, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485347983, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485348807, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485349629, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485350293, "dur": 3683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485353976, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485354237, "dur": 3060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485357297, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485357505, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485358356, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485358418, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485358547, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485359453, "dur": 3584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485363038, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485363199, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485364167, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485364948, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485365743, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485366375, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485367061, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485367722, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485368411, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485369206, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485369919, "dur": 1538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485371488, "dur": 1435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485372923, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485373202, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485373625, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485373873, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485374004, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485374718, "dur": 1499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485376247, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485376491, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485377640, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485377806, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485377870, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485378283, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485378408, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748632485378479, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485378717, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485378841, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485379752, "dur": 450404, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485830915, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485832111, "dur": 131607, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485964508, "dur": 1366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748632485966385, "dur": 196, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632486087330, "dur": 486, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748632485966739, "dur": 121093, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748632486090879, "dur": 711, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748632486091594, "dur": 4152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485207135, "dur": 112666, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485319807, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485320383, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485320536, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485320607, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485320670, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485320814, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485320942, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485321040, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485321172, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485321264, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485321394, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485321494, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485321553, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485321633, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485321747, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485321843, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485321913, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485322009, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748632485322303, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485322460, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748632485322682, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485322840, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485323057, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748632485323233, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748632485323447, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485323795, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748632485323899, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485324012, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485324124, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485324245, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485324722, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485324788, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485325252, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485325305, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485325917, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485326353, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485326509, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485326662, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748632485326813, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485326923, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485327707, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485328389, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485329076, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485329737, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485330388, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485331052, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485331788, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485331846, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485332515, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485333225, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485334187, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485334865, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485335482, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485336086, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485336712, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485337353, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485338021, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485338912, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485339622, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485340346, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485340841, "dur": 15295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485356136, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485356448, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485357178, "dur": 11751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485368929, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485369263, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485369328, "dur": 2162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485371505, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485373747, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485373868, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485373969, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485374785, "dur": 670, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485375470, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485375543, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485375837, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485376442, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll_D2CC97DA1342C9A4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485376595, "dur": 1815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485378411, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748632485378514, "dur": 91999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485470515, "dur": 3456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485474007, "dur": 4808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485478817, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485478913, "dur": 2736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485481650, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485481733, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485484612, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485486860, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485486918, "dur": 2781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485489736, "dur": 3443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485493180, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632485493268, "dur": 4109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748632485497405, "dur": 593417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748632486090844, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748632486090824, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748632486090935, "dur": 665, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748632486091601, "dur": 4163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485207142, "dur": 112669, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485319814, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485320422, "dur": 256, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485320701, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485320963, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485321047, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485321228, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485321294, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485321414, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485321515, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485321781, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485321868, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485321925, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485322016, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748632485322219, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485322713, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485322788, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748632485322875, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748632485322979, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485323059, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748632485323190, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485323854, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485323971, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485324051, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485324280, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485324647, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485324744, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485324863, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485324962, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748632485325260, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485325371, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485325967, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485326244, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485326430, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748632485326612, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485326717, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485327557, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485328249, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485328911, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485329579, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485330171, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485330813, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485331448, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485332175, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485332828, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485333635, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485334297, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485334971, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485335592, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485336193, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485336835, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485337503, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485338139, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485339042, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485339720, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485340456, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485340951, "dur": 1751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485342703, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485343033, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485344115, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485347577, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485347877, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485348123, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485348175, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485348986, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485350138, "dur": 2804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485352943, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485353052, "dur": 3396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485356448, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485356624, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485356977, "dur": 2383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485359361, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485359622, "dur": 3866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485363488, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485363598, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485364388, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485365151, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485365970, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485366543, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485367246, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485367884, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485368535, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485369216, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485369951, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485370893, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485371613, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485372358, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485372717, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485372920, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485373181, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485373257, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485373314, "dur": 1248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485374563, "dur": 862, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485375435, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485375505, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485375929, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485376271, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485376466, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485377174, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485377318, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748632485377389, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485377613, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485377726, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485378430, "dur": 92037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485470469, "dur": 3189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485473705, "dur": 3594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485477340, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485480336, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485480396, "dur": 1995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485482441, "dur": 3203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485485645, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485485895, "dur": 3246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485489169, "dur": 2530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485491700, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485491761, "dur": 4908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748632485496710, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632485497415, "dur": 597991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748632486095459, "dur": 236, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748632486095697, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485207148, "dur": 112691, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485319842, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485320385, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485320505, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485320627, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485320694, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485320782, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485320868, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485320987, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485321038, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485321108, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485321255, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485321390, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485321453, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485321523, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485321588, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485321669, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485321777, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485321838, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485322061, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748632485322581, "dur": 1812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485324394, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485324806, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485324956, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485325186, "dur": 6210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485331396, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485331773, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485333929, "dur": 5666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485339595, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485340126, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485340190, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485340825, "dur": 3888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485344713, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485345076, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485345414, "dur": 4068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485349482, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485349832, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748632485350183, "dur": 2862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485353045, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485353293, "dur": 4104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748632485357451, "dur": 1164, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485468165, "dur": 1165, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485358902, "dur": 110438, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748632485470444, "dur": 4031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485474476, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485474622, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485477147, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485477222, "dur": 2975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485480236, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485482592, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485485119, "dur": 2425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485487544, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485487620, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485490021, "dur": 3131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485493153, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748632485493215, "dur": 4283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748632485497517, "dur": 598237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485207154, "dur": 112691, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485319849, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485320355, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485320489, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485320557, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485320661, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485320798, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485320859, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485320968, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485321059, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485321226, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485321290, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485321344, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485321408, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485321499, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485321556, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485321724, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485321781, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485321853, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485322041, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485322240, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485322336, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748632485322680, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485323097, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748632485323312, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485323669, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485323979, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485324067, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485324409, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748632485324736, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485324947, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748632485325290, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485325438, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485325888, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485326060, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485326259, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485326415, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748632485326542, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485326686, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485327516, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485328210, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485328868, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485329546, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485330199, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485330839, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485331468, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485332190, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485332839, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485333639, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485334309, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485334985, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485335609, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485336212, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485336842, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485337517, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485338155, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485339056, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485339764, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485340548, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485340750, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485343137, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485343284, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485343668, "dur": 4576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485348244, "dur": 695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485348942, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485350004, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485351050, "dur": 3021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485354071, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485354194, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485356985, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485357123, "dur": 2767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485359890, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485360298, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485360436, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485360554, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485361241, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485361926, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485362332, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485363159, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485363810, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485364670, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485365553, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485366263, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485366883, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485367562, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485368243, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485368881, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485369552, "dur": 2063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485371644, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485373168, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485373567, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485373881, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485373977, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485374177, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485374383, "dur": 1088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485375471, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485375535, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485375812, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485376012, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485376234, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485376315, "dur": 2094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485378410, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748632485378506, "dur": 91959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485470465, "dur": 2244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485472710, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485472782, "dur": 2752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485475534, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485475593, "dur": 3418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485479058, "dur": 4668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485483728, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485483789, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485486033, "dur": 3414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485489449, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485489516, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485491362, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485491440, "dur": 3763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748632485495203, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485495399, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485495643, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485495966, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485496055, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485496483, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485496721, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748632485497487, "dur": 598296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485207162, "dur": 112690, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485319855, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485320388, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485320494, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485320652, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485320722, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485320834, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485320933, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485321028, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485321158, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485321256, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485321346, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485321470, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485321565, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485321716, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485321826, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485322260, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485322440, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748632485322587, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748632485322733, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748632485322878, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485322989, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748632485323202, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485323834, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485323958, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485324025, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485324195, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485324330, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485324714, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748632485324771, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485324895, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485325031, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485325990, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485326409, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748632485326510, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485326618, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485326691, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485327951, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485328620, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485329292, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485329963, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485330609, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485331273, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485331981, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485332613, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485333301, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485334195, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485334874, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485335498, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485336107, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485336726, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485337372, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485338033, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485338919, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485339619, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485340340, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485340861, "dur": 2778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485343639, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485344096, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_4EAD86AD4C1B715A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485344151, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485344213, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485344331, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485345458, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485345689, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485345838, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485348565, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485348905, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485349847, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485350328, "dur": 3674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485354002, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485354430, "dur": 3229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485357660, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485357773, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485358139, "dur": 2621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485360760, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485360943, "dur": 1421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485362397, "dur": 4509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485366906, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485367038, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485367709, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485368387, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485369156, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485369882, "dur": 1732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485371641, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485373113, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll_B2C8007ACBA256CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485373803, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485374384, "dur": 1866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485376250, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748632485376377, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485376842, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485376910, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485378419, "dur": 8509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485388547, "dur": 201, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1748632485388749, "dur": 1693, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1748632485390442, "dur": 633, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1748632485386928, "dur": 4150, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485391078, "dur": 79442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485470520, "dur": 2911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485473431, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485473491, "dur": 1733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485475260, "dur": 3236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485478539, "dur": 3396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485481936, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485482213, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485484203, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485484271, "dur": 2931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485487203, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485487404, "dur": 3561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485490965, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748632485491018, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485493473, "dur": 3990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748632485497485, "dur": 598266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485207167, "dur": 112705, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485319874, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485320355, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485320692, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485320817, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485320922, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485321015, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485321152, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485321252, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485321364, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485321477, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485321602, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485321730, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485321782, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485321868, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485322270, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485322709, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485323683, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485324118, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485324173, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485324248, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485324774, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485325110, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485325672, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485326248, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485326451, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748632485326705, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485327554, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485328227, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485328897, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485329573, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485330270, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485330915, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485331549, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485332237, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485332926, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485333718, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485334396, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485335080, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485335688, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485336301, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485336919, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485337590, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485338247, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485339145, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485339813, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485340556, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485340759, "dur": 2329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485343088, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485343239, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485343340, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485343818, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485346228, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485346487, "dur": 11637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485358124, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485358599, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485358692, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485358797, "dur": 3248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485362045, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485362143, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485362316, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485362895, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485363627, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485364437, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485365241, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485366008, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485366579, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485367277, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485367931, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485368585, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485369212, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485369931, "dur": 1595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485371527, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485371600, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485374130, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485374433, "dur": 1864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485376304, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748632485376443, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485377128, "dur": 1295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485378423, "dur": 12658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485391082, "dur": 79380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485470463, "dur": 2462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485472941, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485473088, "dur": 4718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485477806, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485477880, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485480479, "dur": 3430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485483909, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485483962, "dur": 1899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485485899, "dur": 3737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485489675, "dur": 1798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485491509, "dur": 5322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748632485496890, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748632485497520, "dur": 598226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748632486098738, "dur": 1739, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 57575, "tid": 1485895, "ts": 1748632486118751, "dur": 2242, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486121110, "dur": 1791, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 57575, "tid": 1485895, "ts": 1748632486113745, "dur": 10204, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}