{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 57575, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 57575, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 57575, "tid": 2167502, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512852745, "dur": 1111, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512862392, "dur": 752, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 57575, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 57575, "tid": 1, "ts": 1748639509871395, "dur": 18152, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 57575, "tid": 1, "ts": 1748639509889558, "dur": 140648, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 57575, "tid": 1, "ts": 1748639510030216, "dur": 89114, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512863149, "dur": 43, "ph": "X", "name": "", "args": {}}, {"pid": 57575, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509867733, "dur": 35404, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509903140, "dur": 2935163, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509905025, "dur": 15258, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509920296, "dur": 3435, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509923751, "dur": 27271, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509951352, "dur": 457, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509951812, "dur": 101, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509951915, "dur": 6, "ph": "X", "name": "ProcessMessages 8140", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509951924, "dur": 279, "ph": "X", "name": "ReadAsync 8140", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509952206, "dur": 1606, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509953816, "dur": 7, "ph": "X", "name": "ProcessMessages 8100", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509953825, "dur": 78, "ph": "X", "name": "ReadAsync 8100", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509953905, "dur": 1, "ph": "X", "name": "ProcessMessages 1612", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509953908, "dur": 69, "ph": "X", "name": "ReadAsync 1612", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954030, "dur": 2, "ph": "X", "name": "ProcessMessages 1724", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954033, "dur": 122, "ph": "X", "name": "ReadAsync 1724", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954197, "dur": 2, "ph": "X", "name": "ProcessMessages 2235", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954200, "dur": 46, "ph": "X", "name": "ReadAsync 2235", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954247, "dur": 4, "ph": "X", "name": "ProcessMessages 4266", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954327, "dur": 405, "ph": "X", "name": "ReadAsync 4266", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954813, "dur": 5, "ph": "X", "name": "ProcessMessages 5372", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509954818, "dur": 610, "ph": "X", "name": "ReadAsync 5372", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509955435, "dur": 8, "ph": "X", "name": "ProcessMessages 2513", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509955445, "dur": 271, "ph": "X", "name": "ReadAsync 2513", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509955722, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509955723, "dur": 758, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509956483, "dur": 4, "ph": "X", "name": "ProcessMessages 4338", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509956497, "dur": 725, "ph": "X", "name": "ReadAsync 4338", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957224, "dur": 1, "ph": "X", "name": "ProcessMessages 1513", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957226, "dur": 134, "ph": "X", "name": "ReadAsync 1513", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957361, "dur": 3, "ph": "X", "name": "ProcessMessages 3034", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957365, "dur": 39, "ph": "X", "name": "ReadAsync 3034", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957406, "dur": 2, "ph": "X", "name": "ProcessMessages 2417", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957409, "dur": 118, "ph": "X", "name": "ReadAsync 2417", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957537, "dur": 2, "ph": "X", "name": "ProcessMessages 1821", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957540, "dur": 53, "ph": "X", "name": "ReadAsync 1821", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957594, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509957596, "dur": 462, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509958059, "dur": 2, "ph": "X", "name": "ProcessMessages 1900", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509958062, "dur": 148, "ph": "X", "name": "ReadAsync 1900", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509958212, "dur": 2, "ph": "X", "name": "ProcessMessages 1900", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509958215, "dur": 451, "ph": "X", "name": "ReadAsync 1900", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509958667, "dur": 2, "ph": "X", "name": "ProcessMessages 2851", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509958686, "dur": 377, "ph": "X", "name": "ReadAsync 2851", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959065, "dur": 4, "ph": "X", "name": "ProcessMessages 4644", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959070, "dur": 40, "ph": "X", "name": "ReadAsync 4644", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959111, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959113, "dur": 43, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959157, "dur": 1, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959159, "dur": 35, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959206, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959216, "dur": 271, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959489, "dur": 2, "ph": "X", "name": "ProcessMessages 2080", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959492, "dur": 504, "ph": "X", "name": "ReadAsync 2080", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959997, "dur": 1, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509959999, "dur": 32, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509960038, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509960040, "dur": 4141, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964231, "dur": 40, "ph": "X", "name": "ProcessMessages 8185", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964273, "dur": 34, "ph": "X", "name": "ReadAsync 8185", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964308, "dur": 1, "ph": "X", "name": "ProcessMessages 2322", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964310, "dur": 23, "ph": "X", "name": "ReadAsync 2322", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964336, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964365, "dur": 19, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964385, "dur": 36, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964423, "dur": 1, "ph": "X", "name": "ProcessMessages 1654", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964434, "dur": 17, "ph": "X", "name": "ReadAsync 1654", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964454, "dur": 140, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964596, "dur": 39, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964640, "dur": 6, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964647, "dur": 47, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964696, "dur": 132, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964831, "dur": 31, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964863, "dur": 1, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964865, "dur": 34, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964970, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509964971, "dur": 33, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965006, "dur": 2, "ph": "X", "name": "ProcessMessages 2220", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965008, "dur": 110, "ph": "X", "name": "ReadAsync 2220", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965143, "dur": 73, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965219, "dur": 51, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965271, "dur": 5, "ph": "X", "name": "ProcessMessages 1631", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965277, "dur": 93, "ph": "X", "name": "ReadAsync 1631", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965373, "dur": 1, "ph": "X", "name": "ProcessMessages 1627", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965375, "dur": 117, "ph": "X", "name": "ReadAsync 1627", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965499, "dur": 1, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965501, "dur": 29, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965531, "dur": 1, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965537, "dur": 85, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965624, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965625, "dur": 18, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965645, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965646, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965667, "dur": 35, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965704, "dur": 168, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965874, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965876, "dur": 80, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965958, "dur": 8, "ph": "X", "name": "ProcessMessages 4061", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509965976, "dur": 39, "ph": "X", "name": "ReadAsync 4061", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966027, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966028, "dur": 139, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966170, "dur": 1, "ph": "X", "name": "ProcessMessages 1323", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966172, "dur": 40, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966213, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966215, "dur": 58, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966274, "dur": 1, "ph": "X", "name": "ProcessMessages 1634", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966277, "dur": 88, "ph": "X", "name": "ReadAsync 1634", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966366, "dur": 2, "ph": "X", "name": "ProcessMessages 2524", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966370, "dur": 96, "ph": "X", "name": "ReadAsync 2524", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966467, "dur": 1, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966774, "dur": 32, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966807, "dur": 2, "ph": "X", "name": "ProcessMessages 3327", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966818, "dur": 119, "ph": "X", "name": "ReadAsync 3327", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966938, "dur": 1, "ph": "X", "name": "ProcessMessages 1968", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509966941, "dur": 59, "ph": "X", "name": "ReadAsync 1968", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509967004, "dur": 2593, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969599, "dur": 6, "ph": "X", "name": "ProcessMessages 7860", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969811, "dur": 57, "ph": "X", "name": "ReadAsync 7860", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969874, "dur": 3, "ph": "X", "name": "ProcessMessages 4772", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969878, "dur": 23, "ph": "X", "name": "ReadAsync 4772", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969905, "dur": 31, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969938, "dur": 37, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969976, "dur": 1, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509969978, "dur": 40, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970026, "dur": 25, "ph": "X", "name": "ProcessMessages 1391", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970052, "dur": 26, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970079, "dur": 1, "ph": "X", "name": "ProcessMessages 1287", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970081, "dur": 30, "ph": "X", "name": "ReadAsync 1287", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970113, "dur": 1, "ph": "X", "name": "ProcessMessages 1255", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970114, "dur": 46, "ph": "X", "name": "ReadAsync 1255", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970163, "dur": 1, "ph": "X", "name": "ProcessMessages 1243", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970165, "dur": 34, "ph": "X", "name": "ReadAsync 1243", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970200, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970208, "dur": 20, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970242, "dur": 253, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970499, "dur": 1, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970501, "dur": 54, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970559, "dur": 4, "ph": "X", "name": "ProcessMessages 5620", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970565, "dur": 147, "ph": "X", "name": "ReadAsync 5620", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970723, "dur": 246, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970970, "dur": 1, "ph": "X", "name": "ProcessMessages 1758", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509970972, "dur": 420, "ph": "X", "name": "ReadAsync 1758", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509971394, "dur": 10, "ph": "X", "name": "ProcessMessages 5059", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509971405, "dur": 65, "ph": "X", "name": "ReadAsync 5059", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509971471, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509971472, "dur": 141, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509971626, "dur": 732, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509972360, "dur": 4, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509972365, "dur": 403, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509972770, "dur": 1431, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509974203, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509974206, "dur": 937, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509975145, "dur": 393, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509975541, "dur": 1233, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509976776, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509976778, "dur": 23, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509976804, "dur": 701, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509977542, "dur": 6, "ph": "X", "name": "ProcessMessages 1119", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509977556, "dur": 64, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509977621, "dur": 6, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509977628, "dur": 27, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509977659, "dur": 665, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509978328, "dur": 167, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509978498, "dur": 485, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509978986, "dur": 362, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509984168, "dur": 3, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509984180, "dur": 89, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639509986758, "dur": 18, "ph": "X", "name": "ProcessMessages 5875", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040409, "dur": 85, "ph": "X", "name": "ReadAsync 5875", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040505, "dur": 8, "ph": "X", "name": "ProcessMessages 8179", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040514, "dur": 36, "ph": "X", "name": "ReadAsync 8179", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040552, "dur": 1, "ph": "X", "name": "ProcessMessages 1255", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040554, "dur": 23, "ph": "X", "name": "ReadAsync 1255", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040579, "dur": 336, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040917, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510040919, "dur": 261, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510041183, "dur": 539, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510041723, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510041725, "dur": 75, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510041908, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510041910, "dur": 34, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510041945, "dur": 1, "ph": "X", "name": "ProcessMessages 1369", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510041948, "dur": 1149, "ph": "X", "name": "ReadAsync 1369", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510043098, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510043100, "dur": 862, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510043964, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510043966, "dur": 204, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510044172, "dur": 1030, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510045229, "dur": 5, "ph": "X", "name": "ProcessMessages 1558", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510045240, "dur": 166, "ph": "X", "name": "ReadAsync 1558", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510045433, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510045435, "dur": 596, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510046035, "dur": 236, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510046300, "dur": 783, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510047084, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510047086, "dur": 224, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510047312, "dur": 831, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510048146, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510048147, "dur": 1206, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510049397, "dur": 2, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510049427, "dur": 591, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510050019, "dur": 24, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510050045, "dur": 1517, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510051565, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510051566, "dur": 138, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510051707, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510051708, "dur": 673, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510052385, "dur": 632, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510053018, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510053020, "dur": 9709, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510062937, "dur": 8, "ph": "X", "name": "ProcessMessages 8111", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510062946, "dur": 64, "ph": "X", "name": "ReadAsync 8111", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510063013, "dur": 254, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510063271, "dur": 104, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510063377, "dur": 323, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510063703, "dur": 316, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510064022, "dur": 596, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510064637, "dur": 15, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510064653, "dur": 160, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510064815, "dur": 2, "ph": "X", "name": "ProcessMessages 1736", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510064818, "dur": 871, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510065691, "dur": 560, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510066254, "dur": 731, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510066987, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510066988, "dur": 257, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510067249, "dur": 88, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510067339, "dur": 988, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510068329, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510068331, "dur": 1119, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510069464, "dur": 2, "ph": "X", "name": "ProcessMessages 1831", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510069467, "dur": 29, "ph": "X", "name": "ReadAsync 1831", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510069509, "dur": 1460, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510070971, "dur": 1, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510070973, "dur": 290, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510071285, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510071287, "dur": 93, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510071393, "dur": 520, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510071915, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510071917, "dur": 644, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510072568, "dur": 2, "ph": "X", "name": "ProcessMessages 2577", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510072571, "dur": 686, "ph": "X", "name": "ReadAsync 2577", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510073269, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510073270, "dur": 1165, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510074437, "dur": 4, "ph": "X", "name": "ProcessMessages 4202", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510074442, "dur": 1787, "ph": "X", "name": "ReadAsync 4202", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510076231, "dur": 2, "ph": "X", "name": "ProcessMessages 2460", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510076234, "dur": 1576, "ph": "X", "name": "ReadAsync 2460", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510079136, "dur": 690, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510079829, "dur": 173, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080004, "dur": 5, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080010, "dur": 168, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080181, "dur": 342, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080550, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080552, "dur": 188, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080744, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080792, "dur": 136, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080930, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510080932, "dur": 1322, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510082342, "dur": 17, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510082419, "dur": 36864, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510119288, "dur": 32, "ph": "X", "name": "ProcessMessages 4320", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510119322, "dur": 689, "ph": "X", "name": "ReadAsync 4320", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510120014, "dur": 1423, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510121439, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510121441, "dur": 146, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510121590, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510121715, "dur": 448, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510122166, "dur": 424, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510122592, "dur": 392, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510122990, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510122995, "dur": 497, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510124366, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510124368, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510124485, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510124487, "dur": 617, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510125107, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510125109, "dur": 197, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510125309, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510125403, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510125407, "dur": 1281, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510126690, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510126692, "dur": 137, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510126832, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510126989, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510126992, "dur": 397, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510127392, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510127394, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510127487, "dur": 346, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510127837, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510127840, "dur": 289, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128133, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128253, "dur": 235, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128492, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128495, "dur": 109, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128607, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128696, "dur": 207, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128906, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510128921, "dur": 497, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510129421, "dur": 860, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510130283, "dur": 95, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510130384, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510130389, "dur": 63, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510130454, "dur": 596, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131052, "dur": 85, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131140, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131144, "dur": 201, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131347, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131378, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131511, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131514, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131574, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131613, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131645, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131675, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510131942, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132058, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132090, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132187, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132195, "dur": 181, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132379, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132382, "dur": 371, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132757, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132759, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132878, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510132999, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133060, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133139, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133159, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133197, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133228, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133288, "dur": 117, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133408, "dur": 373, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133784, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510133974, "dur": 278, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510134254, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510134260, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510134358, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510134387, "dur": 413, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510134803, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510134806, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510134938, "dur": 87, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135027, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135063, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135222, "dur": 147, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135372, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135389, "dur": 230, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135623, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135671, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135760, "dur": 174, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510135943, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510136016, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510136276, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510136336, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510136505, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510136574, "dur": 512, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510137088, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510137158, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510137165, "dur": 200208, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510337381, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510337386, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510337436, "dur": 105, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510338974, "dur": 42, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510339019, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510339045, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510339087, "dur": 70, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510339158, "dur": 4350, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510343515, "dur": 334, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510343851, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510343853, "dur": 356, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510344211, "dur": 674, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510344888, "dur": 1496, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510346388, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510346390, "dur": 418, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510346811, "dur": 339, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510347158, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510347264, "dur": 1780, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510349047, "dur": 845, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510349898, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510349902, "dur": 671, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510350575, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510350578, "dur": 342, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510350924, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510350926, "dur": 451, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510351380, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510351382, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510351483, "dur": 1394, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510352881, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510352885, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510353013, "dur": 1376, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510354393, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510354609, "dur": 706, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510355317, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510355566, "dur": 360, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510355941, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510355945, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510356130, "dur": 670, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510356804, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510356807, "dur": 664, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510357474, "dur": 752, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510358229, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510358232, "dur": 767, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510359002, "dur": 369, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510359374, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510359412, "dur": 815, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510360230, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510360377, "dur": 1031, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510361411, "dur": 1048, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510362464, "dur": 407, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510362910, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510362911, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510363065, "dur": 540, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510363607, "dur": 585, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510364195, "dur": 1504, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510365702, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510365781, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510365806, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510365955, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366056, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366136, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366158, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366188, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366293, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366300, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366382, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366452, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366496, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366526, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366600, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366624, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366650, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366688, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366731, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366737, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366816, "dur": 117, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366935, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510366997, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367047, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367052, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367173, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367203, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367266, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367371, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367412, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367493, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367564, "dur": 49, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367616, "dur": 91, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367709, "dur": 16, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367727, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367868, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367916, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510367962, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368057, "dur": 166, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368225, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368227, "dur": 92, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368322, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368362, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368487, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368519, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368588, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368667, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368771, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510368869, "dur": 319, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510369190, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510369191, "dur": 262, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510369456, "dur": 118, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510369576, "dur": 108, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639510369687, "dur": 2196840, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512566590, "dur": 268, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512566862, "dur": 2281, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512569165, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512569171, "dur": 5797, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512574980, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512574990, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512575115, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512575123, "dur": 218626, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512793759, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512793765, "dur": 107, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512793907, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512793910, "dur": 195, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512794109, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512794111, "dur": 284, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512794399, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512794444, "dur": 453, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512794902, "dur": 83, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512794987, "dur": 2759, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512797750, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512797753, "dur": 32880, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512830640, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512830642, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512830694, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512830764, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512830822, "dur": 104, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512830927, "dur": 43, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512830970, "dur": 2730, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512833704, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512833706, "dur": 494, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512834203, "dur": 24, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512834228, "dur": 215, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512834445, "dur": 375, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748639512834821, "dur": 3384, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512863195, "dur": 2508, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 57575, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 57575, "tid": 8589934592, "ts": 1748639509862145, "dur": 257274, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 57575, "tid": 8589934592, "ts": 1748639510119422, "dur": 229, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 57575, "tid": 8589934592, "ts": 1748639510119652, "dur": 8299, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512865705, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 57575, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 57575, "tid": 4294967296, "ts": 1748639509646996, "dur": 3192792, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748639509683000, "dur": 170171, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748639512839910, "dur": 7696, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748639512845327, "dur": 42, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748639512847709, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512865731, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748639509898717, "dur": 17192, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748639509915920, "dur": 34040, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748639509950029, "dur": 171, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748639509950200, "dur": 129, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748639509950715, "dur": 363, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748639509951399, "dur": 639, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748639509952464, "dur": 1439, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748639509961191, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748639509961527, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748639509961643, "dur": 2622, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748639509967771, "dur": 1979, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748639509973809, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748639509986197, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748639509988250, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748639509989922, "dur": 50655, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748639510046939, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748639510060265, "dur": 2621, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748639510065504, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnity.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748639509950337, "dur": 127216, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748639510077561, "dur": 2756692, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748639512834408, "dur": 1001, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748639509950256, "dur": 127315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510077575, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748639510077853, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510078345, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510078585, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510078736, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510078832, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510078966, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510079129, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510079293, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510079541, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510079668, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510079870, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510079958, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510080151, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510080287, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510080486, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510080573, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510080703, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510080837, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510080981, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510081060, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510081174, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510081245, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510081398, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510081477, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510081538, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748639510081788, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510082081, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748639510082448, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510083584, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748639510083811, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510084059, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748639510084240, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510084346, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510084427, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510084484, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510084892, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085086, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085224, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085307, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085366, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085435, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748639510085624, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085713, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085800, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085901, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510085994, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510086497, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510086568, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510086656, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510086743, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510086810, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510086863, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748639510087001, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510087075, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510087133, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510087199, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510087879, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510088582, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510089307, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510089964, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510091072, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510092544, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510093293, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510094047, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510094773, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510095477, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510096190, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510097096, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510097865, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510098769, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510099730, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510100613, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510101337, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510101975, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510102707, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510103501, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510104196, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510104839, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510105516, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510106191, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510106959, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510107528, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510108238, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510108958, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510109664, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510110369, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510111021, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510111703, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510112348, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510113010, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510113698, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510114332, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510114991, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510116348, "dur": 2002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510118350, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510119526, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510120831, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510120916, "dur": 1986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510122902, "dur": 768, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510123696, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510123752, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510123836, "dur": 1221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510125057, "dur": 668, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510125754, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748639510125872, "dur": 1330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510127203, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510127496, "dur": 1867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748639510129364, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510129466, "dur": 693, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510337200, "dur": 1974, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510130556, "dur": 208630, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748639510340321, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510342462, "dur": 4669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510347132, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510347256, "dur": 3339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510350624, "dur": 4075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510354700, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510354773, "dur": 1935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510356741, "dur": 2573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510359314, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510359374, "dur": 3462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510362837, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510362935, "dur": 4580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748639510367516, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510367589, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510367697, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510367812, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510367888, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510367951, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748639510368043, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510368185, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510368421, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510368553, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510368639, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510368820, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510368981, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510369038, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748639510369625, "dur": 2464617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639509950266, "dur": 127311, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510077583, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748639510077784, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510077868, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510078420, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510078593, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510078917, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510079002, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510079299, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510079512, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510079757, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510079862, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510080000, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510080142, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510080294, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510080445, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510080501, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510080619, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510080763, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510080906, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510081015, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510081120, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510081181, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510081279, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510081407, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510081498, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510081565, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748639510081765, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510081866, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748639510082206, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748639510082612, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748639510082782, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748639510082929, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748639510083205, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748639510083807, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748639510084088, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748639510084337, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510084441, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748639510084576, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510084628, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748639510084871, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748639510085145, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085261, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085329, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085387, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085498, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085585, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085664, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085762, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085828, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510085950, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510086014, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510086224, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748639510086497, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510086627, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510086768, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510086827, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510086888, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510087012, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510087091, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510087161, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510087856, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510088554, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510089269, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510089922, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510091267, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510092592, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510093363, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510094106, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510094845, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510095550, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510096268, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510097176, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510097985, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510098888, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510099803, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510100726, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510101394, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510102041, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510102802, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510103562, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510104254, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510105099, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510105782, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510106480, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510107140, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510107855, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510108567, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510109261, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510109991, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510110708, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510111513, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510112167, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510112819, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510113478, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510114114, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510114758, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510115628, "dur": 2174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510117803, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510119125, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510120407, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510122699, "dur": 841, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510123544, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_D292A1F7EB1601C3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510123690, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510124027, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510124991, "dur": 1372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510126366, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510126445, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510126849, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510127761, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510127905, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510128052, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510128957, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510129127, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510129841, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510129974, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510130098, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510130306, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510130730, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510130827, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510133682, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510133967, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510134157, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510134819, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510135025, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510135263, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510135762, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510135989, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510136055, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510136269, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510136468, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748639510136583, "dur": 203779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510340362, "dur": 4580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510344943, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510345035, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510347933, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510350352, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510350410, "dur": 2202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510352612, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510352666, "dur": 3473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510356188, "dur": 2504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510358721, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510361428, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510361515, "dur": 3429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510364945, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748639510365001, "dur": 4680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748639510369704, "dur": 2464524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639509950328, "dur": 127324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510077661, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510078362, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510078537, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510078659, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510078857, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510078977, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510079185, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510079324, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510079579, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510079766, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510079931, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510080004, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510080186, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510080308, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510080538, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510080652, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510080886, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510080993, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510081077, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510081133, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510081254, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510081419, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510081490, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510081735, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748639510082656, "dur": 3821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510086478, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510086935, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510087003, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510087138, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510087201, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510087866, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510088573, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510089290, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510089945, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510091276, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510092606, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510093348, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510094097, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510094836, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510095536, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510096252, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510097156, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510097963, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510098846, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510099767, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510100680, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510101372, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510102015, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510102774, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510103526, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510104217, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510104855, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510105533, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510106198, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510106967, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510107799, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510108562, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510109245, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510109947, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510110678, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510111504, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510112157, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510112815, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510113497, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510114105, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510114752, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510115658, "dur": 2150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510117808, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510119112, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510120637, "dur": 971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510121608, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510121727, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510122588, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510123074, "dur": 628, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510123727, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510123917, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510124076, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510124154, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510124858, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510125138, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510126183, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510126946, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510127167, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510128226, "dur": 1402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510129628, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510129784, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510130254, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510131071, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510131436, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510131586, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510131738, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510131815, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510132049, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510132354, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510132740, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510133685, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510133812, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510133992, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510135042, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748639510135306, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510135812, "dur": 204524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510340339, "dur": 3699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510344039, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510344151, "dur": 2872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510347048, "dur": 2005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510349053, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639510349114, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510351523, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510354014, "dur": 3268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510357323, "dur": 3027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510360361, "dur": 3816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510364223, "dur": 5255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748639510369502, "dur": 2428178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748639512797743, "dur": 36496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639509950301, "dur": 127296, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510077601, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510078320, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510078400, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510078682, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510078981, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510079118, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510079348, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510079534, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510079751, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510079844, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510080010, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510080133, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510080317, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510080447, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510080530, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510080652, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510080777, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510080917, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510081021, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510081104, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510081165, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510081267, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510081428, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510081509, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748639510081731, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510082415, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510082479, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748639510082812, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510083864, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510084092, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510084199, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510084317, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510084565, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510084644, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510084907, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510084973, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510085200, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510085336, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510085562, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510085621, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510085700, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510085794, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510085893, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510085990, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510086063, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510086149, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510086205, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510086488, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748639510086636, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510086722, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510086833, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510086900, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510086966, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510087020, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510087093, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510087163, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510088179, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510088959, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510089670, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510090452, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510092280, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510093030, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510093778, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510094544, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510095259, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510095975, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510096854, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510097596, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510098492, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510099430, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510100276, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510101119, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510101732, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510102452, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510103260, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510103964, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510104634, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510105470, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510106147, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510106922, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510107516, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510108223, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510108918, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510109600, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510110332, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510110957, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510111634, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510112259, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510112913, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510113589, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510114222, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510114860, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510116037, "dur": 2152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510118189, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510119328, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510119860, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510119950, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510121047, "dur": 582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510121634, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510121685, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510121745, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510122081, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510123094, "dur": 1393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510124490, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510124574, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510124793, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510125224, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510125379, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510125524, "dur": 1164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510126689, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510126838, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510126952, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510127048, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510128188, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510128474, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510129475, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510129595, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510130380, "dur": 1119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510131499, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510131736, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510132240, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510133817, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510134068, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748639510134265, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510134919, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510135136, "dur": 205189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510340326, "dur": 1957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510342284, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510342361, "dur": 3693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510346076, "dur": 3612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510349689, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510349755, "dur": 3048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510352804, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510352874, "dur": 2648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510355555, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510358140, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510358193, "dur": 2691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510360885, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748639510360974, "dur": 3630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510364649, "dur": 4939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748639510369618, "dur": 2464653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639509950306, "dur": 127302, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510077612, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510078329, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510078399, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510078530, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510078584, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510078787, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510078879, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510079157, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510079270, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510079531, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510079721, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510079934, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510080007, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510080182, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510080312, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510080448, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510080566, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510080661, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510080813, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510080921, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510081106, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510081242, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510081362, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510081418, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510081496, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510081597, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510081790, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510082367, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510082904, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748639510083226, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510083668, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510083839, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510084192, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510084298, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510084607, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510084867, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510084996, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510085083, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085168, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085270, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085336, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085393, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085507, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085618, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085693, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085787, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085868, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510085968, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510086034, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510086233, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510086630, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510086729, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510086834, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748639510086892, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510087018, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510087088, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510087162, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510087217, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510087903, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510088596, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510089325, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510089988, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510091364, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510092629, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510093382, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510094117, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510094851, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510095547, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510096264, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510097167, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510097976, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510098856, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510099774, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510100696, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510101380, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510102037, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510102788, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510103551, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510104245, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510104881, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510105558, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510106240, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510106998, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510107803, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510108508, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510109208, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510109923, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510110649, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510111243, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510111912, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510112539, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510113216, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510113901, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510114677, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510115488, "dur": 2151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510117639, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510118966, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510120308, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510121284, "dur": 2230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510123523, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_4EAD86AD4C1B715A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510123671, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510123939, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510124965, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510125150, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510125323, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510125437, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510126233, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510126647, "dur": 1897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510128544, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510128657, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510128795, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510128924, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510129865, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510130024, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510131076, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510131219, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510132011, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510132178, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510132325, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510133467, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510133947, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510134135, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748639510134440, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510134711, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510134976, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510135440, "dur": 204882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510340324, "dur": 2859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510343228, "dur": 3514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510346785, "dur": 4474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510351292, "dur": 4499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510355840, "dur": 3562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510359403, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510359466, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510362960, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510363025, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748639510365696, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510365866, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510366244, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510366452, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510366585, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748639510366646, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510366801, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510366902, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510367100, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510367165, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510367386, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510367658, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510367780, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510367895, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510368008, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510368095, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510368276, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510368364, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510368506, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510368741, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748639510368796, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510368920, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510369011, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639510369511, "dur": 2464117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748639512833697, "dur": 475, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748639512834175, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639509950324, "dur": 127316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510077645, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510078295, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510078493, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510078736, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510078810, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510078934, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510079042, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510079357, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510079539, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510079773, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510079946, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510080072, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510080231, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510080427, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510080621, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510080715, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510080871, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510080940, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510081117, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510081223, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510081343, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510081430, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510081582, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748639510081956, "dur": 6574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510088530, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510088842, "dur": 3113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510092009, "dur": 16159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510108169, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510108523, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510110720, "dur": 7521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510118242, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510118723, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510121154, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510122678, "dur": 1565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510124259, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510124329, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510124470, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510124834, "dur": 877, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510125741, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510125885, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510128133, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510128427, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510128494, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510128710, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510129297, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510129861, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510130219, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510132562, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510132922, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510133026, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510133491, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510134033, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510134236, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510135045, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748639510135279, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510135828, "dur": 204514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510340342, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510343836, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510343953, "dur": 3322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510347299, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510350932, "dur": 3496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510354429, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510354487, "dur": 2219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510356757, "dur": 3095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510359905, "dur": 3607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510363512, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510363617, "dur": 5582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748639510369234, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748639510369706, "dur": 2464529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639509950329, "dur": 127343, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510077681, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510078364, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510078513, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510078575, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510078924, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510079007, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510079190, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510079338, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510079559, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510079753, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510079939, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510080016, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510080222, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510080343, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510080457, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510080570, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510080700, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510080899, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510081005, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510081235, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510081356, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510081456, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510081640, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510081827, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510081980, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748639510082287, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510082395, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510082713, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510082910, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748639510083294, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510084013, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748639510084102, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510084375, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510084636, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510084879, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085081, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085172, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085300, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085405, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085522, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085633, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085723, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085809, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510085914, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086000, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086079, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086157, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086253, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748639510086647, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086738, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086804, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086916, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510086980, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510087048, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510087104, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510087185, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510087873, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510088579, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510089301, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510089961, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510091302, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510092618, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510093354, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510094091, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510094825, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510095524, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510096240, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510097141, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510097924, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510098843, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510099808, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510100724, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510101389, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510102033, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510102785, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510103544, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510104236, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510104876, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510105568, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510106247, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510107007, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510107812, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510108526, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510109223, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510109949, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510110664, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510111259, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510111924, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510112561, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510113223, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510113914, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510114705, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510115525, "dur": 2168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510117693, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510119065, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510120043, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510120143, "dur": 1236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510121379, "dur": 1541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510122941, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510123012, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510123941, "dur": 1062, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510125032, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510125392, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510126112, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510126217, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510127307, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510127986, "dur": 597, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1748639510128583, "dur": 2215, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1748639510130798, "dur": 699, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1748639510127690, "dur": 3807, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510131498, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510131579, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510131781, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510132199, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510133044, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510133121, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510135372, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510135616, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510135708, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510136177, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510136307, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748639510136369, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510136704, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639510136837, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639510138147, "dur": 2427420, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639512568686, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748639512567746, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748639512568971, "dur": 3410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748639512574104, "dur": 892, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639512793521, "dur": 944, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748639512576333, "dur": 218188, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748639512797746, "dur": 36495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639509950331, "dur": 127353, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510077701, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510078409, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_620BC00CDC1CB284.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510078564, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510078697, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510078792, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510078984, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510079158, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510079377, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510079571, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510079750, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510079840, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510079997, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510080083, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510080324, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510080516, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510080708, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510080866, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510081000, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510081073, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510081157, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510081216, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510081351, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510081410, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510081469, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510081843, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510082641, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510082933, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748639510083655, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510083761, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510083855, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510084292, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510084496, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748639510084675, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510084930, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510084986, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510085236, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085312, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085380, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085464, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085533, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085642, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085748, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085817, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510085929, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086008, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086108, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086165, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086243, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510086545, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086654, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086751, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086819, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086891, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510086961, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510087024, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510087129, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748639510087227, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510087895, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510088590, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510089312, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510089966, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510091322, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510092608, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510093359, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510094102, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510094831, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510095532, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510096248, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510097153, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510097941, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510098848, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510099737, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510100671, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510101354, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510101993, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510102724, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510103513, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510104206, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510104851, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510105529, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510106196, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510106965, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510107763, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510108527, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510109218, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510109937, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510110669, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510111265, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510111944, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510112594, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510113269, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510113940, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510114697, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510115524, "dur": 2178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510117702, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510119024, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510119700, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510119758, "dur": 3201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510122959, "dur": 2064, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510125136, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510125559, "dur": 4790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510130350, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510130545, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510130955, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510133045, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510133137, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510133345, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510134316, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510134397, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510135061, "dur": 1413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510136476, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748639510136632, "dur": 203752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510340384, "dur": 3884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510344269, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510344374, "dur": 2800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510347212, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510350273, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510350335, "dur": 2624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510353000, "dur": 3066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510356110, "dur": 2897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510359059, "dur": 3414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510362474, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510362570, "dur": 3797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639510366367, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510366536, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510366719, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510366803, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510366929, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510367092, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510367171, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748639510367297, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510367483, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748639510367606, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510367746, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510367855, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510368098, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510368257, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510368354, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510368596, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510368721, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748639510368889, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510368977, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639510369236, "dur": 2198511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639512568042, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748639512567751, "dur": 4376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639512573587, "dur": 1006, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639512830568, "dur": 345, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748639512576183, "dur": 254737, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748639512833606, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748639512833603, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748639512833717, "dur": 470, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748639512837075, "dur": 760, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 57575, "tid": 2167502, "ts": 1748639512866503, "dur": 3655, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512870188, "dur": 2186, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 57575, "tid": 2167502, "ts": 1748639512858442, "dur": 15665, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}