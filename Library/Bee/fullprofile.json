{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 57575, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 57575, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 57575, "tid": 15, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 57575, "tid": 15, "ts": 1748622426745090, "dur": 617, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 57575, "tid": 15, "ts": 1748622426748338, "dur": 527, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 57575, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 57575, "tid": 1, "ts": 1748622426450024, "dur": 6169, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 57575, "tid": 1, "ts": 1748622426456197, "dur": 45662, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 57575, "tid": 1, "ts": 1748622426501865, "dur": 36451, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 57575, "tid": 15, "ts": 1748622426748868, "dur": 187, "ph": "X", "name": "", "args": {}}, {"pid": 57575, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426448538, "dur": 8049, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426456589, "dur": 282098, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426457294, "dur": 3512, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426460815, "dur": 727, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426461546, "dur": 15853, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477405, "dur": 301, "ph": "X", "name": "ProcessMessages 6082", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477708, "dur": 38, "ph": "X", "name": "ReadAsync 6082", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477747, "dur": 6, "ph": "X", "name": "ProcessMessages 8166", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477755, "dur": 21, "ph": "X", "name": "ReadAsync 8166", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477779, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477781, "dur": 63, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477846, "dur": 1, "ph": "X", "name": "ProcessMessages 1630", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477849, "dur": 24, "ph": "X", "name": "ReadAsync 1630", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477875, "dur": 28, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477905, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477906, "dur": 37, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477945, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477947, "dur": 41, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477989, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426477991, "dur": 35, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478028, "dur": 1, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478030, "dur": 22, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478054, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478057, "dur": 33, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478091, "dur": 1, "ph": "X", "name": "ProcessMessages 1099", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478092, "dur": 28, "ph": "X", "name": "ReadAsync 1099", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478122, "dur": 1, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478124, "dur": 22, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478149, "dur": 29, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478181, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478182, "dur": 27, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478211, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478212, "dur": 18, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478233, "dur": 28, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478262, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478264, "dur": 20, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478287, "dur": 47, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478335, "dur": 1, "ph": "X", "name": "ProcessMessages 1309", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478337, "dur": 33, "ph": "X", "name": "ReadAsync 1309", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478371, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478373, "dur": 22, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478399, "dur": 39, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478438, "dur": 1, "ph": "X", "name": "ProcessMessages 1398", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478440, "dur": 21, "ph": "X", "name": "ReadAsync 1398", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478464, "dur": 26, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478491, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478493, "dur": 34, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478528, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478530, "dur": 40, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478572, "dur": 1, "ph": "X", "name": "ProcessMessages 1409", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478573, "dur": 20, "ph": "X", "name": "ReadAsync 1409", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478595, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478596, "dur": 28, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478626, "dur": 5, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478632, "dur": 24, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478659, "dur": 17, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478678, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478700, "dur": 186, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426478889, "dur": 887, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426479778, "dur": 2, "ph": "X", "name": "ProcessMessages 1961", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426479781, "dur": 32, "ph": "X", "name": "ReadAsync 1961", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426479814, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426479816, "dur": 1159, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426480978, "dur": 5, "ph": "X", "name": "ProcessMessages 4705", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426480984, "dur": 37, "ph": "X", "name": "ReadAsync 4705", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481024, "dur": 1, "ph": "X", "name": "ProcessMessages 1279", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481026, "dur": 29, "ph": "X", "name": "ReadAsync 1279", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481057, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481059, "dur": 318, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481379, "dur": 157, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481538, "dur": 1, "ph": "X", "name": "ProcessMessages 1226", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481540, "dur": 40, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481582, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481584, "dur": 86, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481671, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481673, "dur": 31, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481705, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481706, "dur": 29, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481738, "dur": 132, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481871, "dur": 1, "ph": "X", "name": "ProcessMessages 1287", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481873, "dur": 27, "ph": "X", "name": "ReadAsync 1287", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481902, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481903, "dur": 35, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481941, "dur": 1, "ph": "X", "name": "ProcessMessages 1238", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481943, "dur": 44, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481988, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426481989, "dur": 469, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482460, "dur": 2, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482464, "dur": 38, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482503, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482505, "dur": 261, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482767, "dur": 2, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482770, "dur": 33, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482805, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426482807, "dur": 210, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483019, "dur": 1, "ph": "X", "name": "ProcessMessages 1500", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483021, "dur": 28, "ph": "X", "name": "ReadAsync 1500", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483050, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483052, "dur": 263, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483317, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483319, "dur": 40, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483360, "dur": 1, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483362, "dur": 354, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483717, "dur": 1, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483719, "dur": 39, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483760, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483762, "dur": 43, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483806, "dur": 1, "ph": "X", "name": "ProcessMessages 1261", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483809, "dur": 34, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483844, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426483845, "dur": 174, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484021, "dur": 1, "ph": "X", "name": "ProcessMessages 1313", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484023, "dur": 51, "ph": "X", "name": "ReadAsync 1313", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484075, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484077, "dur": 278, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484356, "dur": 1, "ph": "X", "name": "ProcessMessages 1349", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484358, "dur": 36, "ph": "X", "name": "ReadAsync 1349", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484396, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484398, "dur": 218, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484617, "dur": 1, "ph": "X", "name": "ProcessMessages 1449", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484619, "dur": 34, "ph": "X", "name": "ReadAsync 1449", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484654, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426484656, "dur": 473, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485130, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485132, "dur": 34, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485167, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485169, "dur": 26, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485196, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485198, "dur": 40, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485239, "dur": 1, "ph": "X", "name": "ProcessMessages 1209", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485241, "dur": 27, "ph": "X", "name": "ReadAsync 1209", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485269, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426485272, "dur": 1043, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486318, "dur": 6, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486324, "dur": 26, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486352, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486354, "dur": 288, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486643, "dur": 1, "ph": "X", "name": "ProcessMessages 1640", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486645, "dur": 29, "ph": "X", "name": "ReadAsync 1640", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486676, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426486677, "dur": 910, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487589, "dur": 5, "ph": "X", "name": "ProcessMessages 6800", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487595, "dur": 72, "ph": "X", "name": "ReadAsync 6800", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487668, "dur": 1, "ph": "X", "name": "ProcessMessages 1370", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487671, "dur": 35, "ph": "X", "name": "ReadAsync 1370", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487708, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487710, "dur": 162, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487873, "dur": 1, "ph": "X", "name": "ProcessMessages 1435", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487875, "dur": 27, "ph": "X", "name": "ReadAsync 1435", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487903, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426487905, "dur": 97, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488004, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488006, "dur": 39, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488045, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488047, "dur": 38, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488088, "dur": 130, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488220, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488222, "dur": 37, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488260, "dur": 1, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488262, "dur": 35, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488298, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488301, "dur": 39, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488341, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488343, "dur": 254, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488599, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488600, "dur": 36, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488638, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488640, "dur": 19, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488662, "dur": 238, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488901, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488903, "dur": 33, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488938, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488940, "dur": 32, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488973, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426488975, "dur": 34, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489010, "dur": 7, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489018, "dur": 22, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489043, "dur": 3, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489048, "dur": 28, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489077, "dur": 1, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489079, "dur": 73, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489155, "dur": 89, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489245, "dur": 1, "ph": "X", "name": "ProcessMessages 1723", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489247, "dur": 27, "ph": "X", "name": "ReadAsync 1723", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489278, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489280, "dur": 149, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489432, "dur": 118, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489551, "dur": 1, "ph": "X", "name": "ProcessMessages 1218", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489553, "dur": 37, "ph": "X", "name": "ReadAsync 1218", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426489593, "dur": 551, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426490147, "dur": 1, "ph": "X", "name": "ProcessMessages 1298", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426490149, "dur": 972, "ph": "X", "name": "ReadAsync 1298", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491123, "dur": 6, "ph": "X", "name": "ProcessMessages 8159", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491131, "dur": 20, "ph": "X", "name": "ReadAsync 8159", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491152, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491153, "dur": 17, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491186, "dur": 18, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491207, "dur": 163, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491371, "dur": 1, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491373, "dur": 41, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491415, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491417, "dur": 67, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491485, "dur": 1, "ph": "X", "name": "ProcessMessages 1694", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491487, "dur": 38, "ph": "X", "name": "ReadAsync 1694", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491527, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491529, "dur": 36, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491567, "dur": 1, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491569, "dur": 34, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491604, "dur": 1, "ph": "X", "name": "ProcessMessages 1329", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491606, "dur": 33, "ph": "X", "name": "ReadAsync 1329", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491640, "dur": 2, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491642, "dur": 27, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491671, "dur": 1, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491673, "dur": 35, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491711, "dur": 129, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491843, "dur": 1, "ph": "X", "name": "ProcessMessages 1584", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491844, "dur": 35, "ph": "X", "name": "ReadAsync 1584", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491880, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491882, "dur": 36, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491919, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491921, "dur": 33, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491956, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426491957, "dur": 166, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492134, "dur": 26, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492161, "dur": 1, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492163, "dur": 31, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492196, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492198, "dur": 30, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492230, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492231, "dur": 31, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492264, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492266, "dur": 144, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492412, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492414, "dur": 28, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492443, "dur": 1, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492445, "dur": 17, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492465, "dur": 155, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492623, "dur": 148, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492774, "dur": 18, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492794, "dur": 18, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492813, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492815, "dur": 174, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426492992, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493016, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493018, "dur": 40, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493059, "dur": 1, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493061, "dur": 20, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493084, "dur": 111, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493196, "dur": 1, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493198, "dur": 18, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493220, "dur": 34, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493255, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493257, "dur": 146, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493405, "dur": 29, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493435, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493439, "dur": 34, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493475, "dur": 180, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426493658, "dur": 1206, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426494865, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426494867, "dur": 170, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426495041, "dur": 138, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426495182, "dur": 627, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426495812, "dur": 179, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426495995, "dur": 899, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426496897, "dur": 159, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426497059, "dur": 926, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426497988, "dur": 75, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426498065, "dur": 293, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426498361, "dur": 162, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426498526, "dur": 937, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426499464, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426499466, "dur": 74, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426499542, "dur": 296, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426499841, "dur": 153, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426499997, "dur": 610, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426500608, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426500611, "dur": 146, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426500758, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426500761, "dur": 596, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426501361, "dur": 167, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426501530, "dur": 620, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426502153, "dur": 217, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426502373, "dur": 741, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426503115, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426503117, "dur": 129, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426503249, "dur": 348, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426503600, "dur": 192, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426503795, "dur": 179, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426503977, "dur": 19, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426503999, "dur": 312, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426504314, "dur": 203, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426504519, "dur": 180, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426504702, "dur": 170, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426504875, "dur": 1052, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426505929, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426505931, "dur": 83, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426506017, "dur": 173, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426506192, "dur": 566, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426506760, "dur": 161, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426506924, "dur": 569, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426507496, "dur": 29, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426507528, "dur": 161, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426507692, "dur": 666, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426508361, "dur": 173, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426508535, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426508537, "dur": 39, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426508577, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426508579, "dur": 32, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426508614, "dur": 837, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426509454, "dur": 155, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426509611, "dur": 2480, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512093, "dur": 3, "ph": "X", "name": "ProcessMessages 4021", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512097, "dur": 108, "ph": "X", "name": "ReadAsync 4021", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512207, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512208, "dur": 20, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512230, "dur": 25, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512259, "dur": 37, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512299, "dur": 584, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426512885, "dur": 162, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426513049, "dur": 765, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426513817, "dur": 157, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426513977, "dur": 621, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426514600, "dur": 166, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426514768, "dur": 47, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426514817, "dur": 1, "ph": "X", "name": "ProcessMessages 1432", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426514819, "dur": 31, "ph": "X", "name": "ReadAsync 1432", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426514853, "dur": 808, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426515663, "dur": 624, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426516290, "dur": 85, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426516378, "dur": 150, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426516530, "dur": 585, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426517118, "dur": 161, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426517284, "dur": 660, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426517947, "dur": 35, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426517985, "dur": 146, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426518136, "dur": 599, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426518739, "dur": 163, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426518904, "dur": 909, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426519816, "dur": 82, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426519901, "dur": 685, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426520587, "dur": 1, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426520589, "dur": 202, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426520793, "dur": 155, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426520951, "dur": 622, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426521576, "dur": 170, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426521748, "dur": 630, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426522381, "dur": 148, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426522531, "dur": 621, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426523155, "dur": 2232, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426525390, "dur": 2, "ph": "X", "name": "ProcessMessages 2189", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426525393, "dur": 506, "ph": "X", "name": "ReadAsync 2189", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426525901, "dur": 180, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426526084, "dur": 947, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527034, "dur": 171, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527207, "dur": 212, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527422, "dur": 175, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527602, "dur": 70, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527675, "dur": 43, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527721, "dur": 71, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527794, "dur": 17, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426527814, "dur": 530, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426528346, "dur": 663, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529013, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529016, "dur": 134, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529152, "dur": 218, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529372, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529373, "dur": 20, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529395, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529399, "dur": 38, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529439, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426529441, "dur": 1221, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426530665, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426530666, "dur": 25, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426530696, "dur": 728, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426531427, "dur": 25, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426531455, "dur": 124, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426531583, "dur": 2719, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426534308, "dur": 7, "ph": "X", "name": "ProcessMessages 4062", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426534316, "dur": 1190, "ph": "X", "name": "ReadAsync 4062", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426535509, "dur": 116, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426535628, "dur": 41, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426535670, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426535672, "dur": 37, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426535712, "dur": 908, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426536623, "dur": 142, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426536767, "dur": 562, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426537332, "dur": 169, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426537505, "dur": 23, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426537530, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426537550, "dur": 24, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426537576, "dur": 846, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426538425, "dur": 195, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426538621, "dur": 1, "ph": "X", "name": "ProcessMessages 1635", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426538624, "dur": 33, "ph": "X", "name": "ReadAsync 1635", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426538660, "dur": 184, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426538846, "dur": 162, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539010, "dur": 1, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539011, "dur": 79, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539092, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539094, "dur": 185, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539281, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539282, "dur": 219, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539503, "dur": 1, "ph": "X", "name": "ProcessMessages 1573", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539506, "dur": 33, "ph": "X", "name": "ReadAsync 1573", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539542, "dur": 372, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426539917, "dur": 538, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426540457, "dur": 129, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426540588, "dur": 430, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541019, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541021, "dur": 109, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541132, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541281, "dur": 124, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541408, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541543, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541545, "dur": 94, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541642, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541677, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541772, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541831, "dur": 46, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541880, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426541936, "dur": 139, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542083, "dur": 20, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542104, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542197, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542286, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542351, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542385, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542535, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542635, "dur": 347, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426542985, "dur": 183, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543169, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543171, "dur": 110, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543287, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543291, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543421, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543423, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543624, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543626, "dur": 169, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543816, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426543818, "dur": 176, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544008, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544010, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544049, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544097, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544260, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544263, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544300, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544363, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544538, "dur": 150, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544691, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544727, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544728, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544763, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544835, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544874, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426544996, "dur": 47, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545045, "dur": 222, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545269, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545428, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545429, "dur": 132, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545564, "dur": 234, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545799, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545801, "dur": 145, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545948, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426545987, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546014, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546105, "dur": 38, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546172, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546251, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546296, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546488, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546592, "dur": 51, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546645, "dur": 243, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546891, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426546965, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547109, "dur": 26, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547149, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547193, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547290, "dur": 99, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547391, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547472, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547534, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547554, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547729, "dur": 249, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426547984, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548191, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548193, "dur": 211, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548405, "dur": 17, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548424, "dur": 57, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548484, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548544, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548572, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548666, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548729, "dur": 167, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548898, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548923, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426548943, "dur": 236, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549181, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549274, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549279, "dur": 67, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549347, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549349, "dur": 104, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549455, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549500, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549525, "dur": 145, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549672, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549674, "dur": 76, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549753, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549870, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549871, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549926, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426549928, "dur": 175, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550104, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550106, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550146, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550241, "dur": 96, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550340, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550394, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550421, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550506, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550702, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550787, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550968, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426550970, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551006, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551061, "dur": 66, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551130, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551160, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551217, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551248, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551296, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551430, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551432, "dur": 69, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551505, "dur": 79, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551586, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551649, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551787, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551792, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426551836, "dur": 183, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552022, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552075, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552108, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552171, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552220, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552319, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552393, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552542, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552549, "dur": 111, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552663, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552708, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552709, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552742, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552834, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552877, "dur": 58, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552937, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552938, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426552984, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553025, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553061, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553084, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553113, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553151, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553205, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553225, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426553228, "dur": 1706, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426554936, "dur": 19660, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426574601, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426574603, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426574680, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426574787, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426574860, "dur": 2446, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426577309, "dur": 7058, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426584372, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426584374, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426584408, "dur": 950, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426585360, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426585530, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426585534, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426585568, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426585772, "dur": 1248, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426587022, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426587139, "dur": 776, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426587919, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426588025, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426588053, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426588158, "dur": 501, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426588662, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426588770, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426589038, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426589181, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426589286, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426589361, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426589490, "dur": 498, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426589990, "dur": 246, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426590239, "dur": 649, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426590890, "dur": 549, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426591443, "dur": 192, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426591637, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426591736, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426591822, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426591883, "dur": 324, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426592210, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426592237, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426592282, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426592388, "dur": 232, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426592622, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426592899, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426592977, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426593011, "dur": 369, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426593382, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426593449, "dur": 554, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426594004, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426594006, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426594052, "dur": 415, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426594469, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426594674, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426594677, "dur": 351, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426595030, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426595202, "dur": 243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426595447, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426595655, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426595766, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426595924, "dur": 509, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426596435, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426596466, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426596574, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426596637, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426596638, "dur": 80, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426596721, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426596831, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426597022, "dur": 236, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426597261, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426597476, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426597604, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426597774, "dur": 747, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426598524, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426598630, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426598633, "dur": 361, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426598997, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426599252, "dur": 198, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426599453, "dur": 355, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426599812, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426600013, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426600160, "dur": 256, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426600418, "dur": 397, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426600818, "dur": 202, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426601022, "dur": 757, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426601781, "dur": 359, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602141, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602179, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602257, "dur": 235, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602495, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602656, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602703, "dur": 81, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602786, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426602990, "dur": 113, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603105, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603185, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603259, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603398, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603487, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603673, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603725, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426603933, "dur": 416, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426604352, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426604529, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426604710, "dur": 1144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426605858, "dur": 275, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426606136, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426606235, "dur": 339, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426606577, "dur": 355, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426606935, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426607114, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426607231, "dur": 263, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426607496, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426607722, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426607855, "dur": 201, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426608058, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426608192, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426608300, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426608304, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426608483, "dur": 382, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426608868, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426608984, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426609132, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426609193, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426609264, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426609342, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426609371, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426609578, "dur": 316, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426609896, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610038, "dur": 346, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610392, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610396, "dur": 137, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610537, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610539, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610663, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610847, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610946, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426610996, "dur": 401, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426611399, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426611535, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426611579, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426611658, "dur": 837, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426612502, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426612535, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426612603, "dur": 14, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426612619, "dur": 237, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426612858, "dur": 427, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426613289, "dur": 94379, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707675, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707683, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707725, "dur": 28, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707765, "dur": 38, "ph": "X", "name": "ReadAsync 7465", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707808, "dur": 32, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707842, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707878, "dur": 32, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426707912, "dur": 935, "ph": "X", "name": "ProcessMessages 2812", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426708849, "dur": 3035, "ph": "X", "name": "ReadAsync 2812", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426711890, "dur": 477, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426712369, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426712759, "dur": 360, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426713121, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426713263, "dur": 742, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426714007, "dur": 1038, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426715048, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426715276, "dur": 1122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426716403, "dur": 568, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426716973, "dur": 434, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426717410, "dur": 596, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426718010, "dur": 219, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426718232, "dur": 381, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426718615, "dur": 455, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426719078, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426719083, "dur": 1219, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426720304, "dur": 474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426720781, "dur": 612, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426721397, "dur": 273, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426721673, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426721829, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426722044, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426722129, "dur": 446, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426722577, "dur": 1178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426723759, "dur": 788, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426724549, "dur": 892, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426725444, "dur": 715, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426726160, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426726162, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426726237, "dur": 269, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426726513, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426726517, "dur": 1191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426727710, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426727802, "dur": 481, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426728285, "dur": 1164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426729451, "dur": 595, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426730050, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426730109, "dur": 585, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426730696, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426730866, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731023, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731089, "dur": 236, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731328, "dur": 176, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731506, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731563, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731674, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731776, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731924, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426731988, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732078, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732111, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732142, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732316, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732401, "dur": 62, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732466, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732503, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732531, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732692, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732777, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732857, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732895, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426732961, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733022, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733039, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733148, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733191, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733268, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733366, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733401, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733429, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733464, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733540, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733593, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733634, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733666, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733693, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733723, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733755, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733817, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733898, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733899, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733933, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426733962, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734030, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734090, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734191, "dur": 82, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734276, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734300, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734322, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734366, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734403, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734428, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734452, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734526, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734603, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734632, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734666, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734697, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734775, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734837, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734939, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734966, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426734988, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426735044, "dur": 195, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426735241, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426735306, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426735340, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426735450, "dur": 153, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426735604, "dur": 175, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 57575, "tid": 12884901888, "ts": 1748622426735780, "dur": 2880, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 57575, "tid": 15, "ts": 1748622426749057, "dur": 1311, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 57575, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 57575, "tid": 8589934592, "ts": 1748622426446651, "dur": 91753, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 57575, "tid": 8589934592, "ts": 1748622426538407, "dur": 22, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 57575, "tid": 8589934592, "ts": 1748622426538430, "dur": 1720, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 57575, "tid": 15, "ts": 1748622426750370, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 57575, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 57575, "tid": 4294967296, "ts": 1748622426415542, "dur": 324010, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748622426424805, "dur": 17586, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748622426739678, "dur": 3976, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748622426741423, "dur": 1445, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 57575, "tid": 4294967296, "ts": 1748622426743698, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 57575, "tid": 15, "ts": 1748622426750381, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748622426450250, "dur": 3831, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748622426454092, "dur": 23548, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748622426477679, "dur": 182, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748622426477861, "dur": 143, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748622426478572, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748622426487104, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748622426487784, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748622426491788, "dur": 231, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748622426478009, "dur": 62728, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748622426540745, "dur": 195612, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748622426736458, "dur": 753, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748622426477914, "dur": 62839, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426540758, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748622426540940, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426541319, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426541504, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426541784, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426541847, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426542391, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426542612, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426542786, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426542905, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426542987, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426543304, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426543401, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426543681, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426543813, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426543933, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426544046, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426544234, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426544463, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426544584, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426544796, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426544914, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426545016, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426545103, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426545247, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426545351, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426545523, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426545627, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426545782, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426545878, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426546087, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426546306, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426546363, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426546574, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426546842, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426547001, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426547183, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748622426547395, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426547479, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426547783, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426547856, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426547964, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748622426548169, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426548931, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426549246, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426549313, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426549413, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426549465, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426549579, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426550199, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426550389, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426550467, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426550611, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426550794, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426550993, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748622426551108, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426551232, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426551645, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748622426551850, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426551923, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426551997, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426552085, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426552354, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426552428, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426552678, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748622426553079, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426553369, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426553663, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426553752, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426553824, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426553955, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426554020, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426555039, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426555655, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426556384, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426557037, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426557700, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426558446, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426559415, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426560380, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426561445, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426562455, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426563414, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426564490, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426565393, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426566389, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426567169, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426567983, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426568998, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426569868, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426570627, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426571678, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426572458, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426573507, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426574253, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426575079, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426576137, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426577006, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426577803, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426578983, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426580014, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426581131, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426581994, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426582973, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426583815, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426584586, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426585697, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426586119, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426586270, "dur": 3096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426589366, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426589500, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_D292A1F7EB1601C3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426589564, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426589622, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426590205, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426590285, "dur": 3829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426594114, "dur": 728, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426594846, "dur": 1184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426596030, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426596287, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426596427, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426596770, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426597174, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426597785, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426598951, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426599458, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426600677, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426600853, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426601231, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426602431, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426602627, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426602708, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426602786, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426603403, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426603539, "dur": 1670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426605209, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426605563, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426607087, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426607302, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748622426607618, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426608817, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426609096, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426609626, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426609957, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426610999, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426611113, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426611193, "dur": 98719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426709913, "dur": 4166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426714079, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426714149, "dur": 3101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426717250, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426717320, "dur": 2122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426719442, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426719526, "dur": 3418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426722944, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426722998, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426725259, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426725578, "dur": 2951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426728529, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426728608, "dur": 3081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748622426731690, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426731754, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426731991, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426733111, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426733283, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426733434, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426733599, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426733878, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426734055, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426734115, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426734289, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426734466, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426734554, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426734632, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426734909, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426735043, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426735125, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426735393, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426735519, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426735629, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748622426735849, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426477922, "dur": 62839, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426540765, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748622426541018, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426542574, "dur": 273, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1748622426542848, "dur": 3116, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1748622426545964, "dur": 2338, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1748622426541314, "dur": 6988, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426548394, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426549031, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426549145, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426549357, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748622426549500, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426549596, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748622426550179, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426550277, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426550385, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426550550, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426550721, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426550787, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748622426551077, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748622426551190, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426551302, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426551566, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426551750, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426551815, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426551937, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426552012, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426552099, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426552238, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748622426552350, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426552418, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426552638, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748622426552833, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748622426553031, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748622426553391, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426553631, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426553814, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426553964, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426554099, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426555145, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426555735, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426556466, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426557164, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426557842, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426558780, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426559797, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426560855, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426561916, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426563121, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426564117, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426565009, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426565942, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426566850, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426567638, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426568517, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426569440, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426570289, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426571183, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426572138, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426572725, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426573574, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426574319, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426575136, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426576225, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426577064, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426577829, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426578841, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426579912, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426580941, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426581865, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426582913, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426583730, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426584530, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426585615, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426586188, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426586450, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426589576, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426590032, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426590102, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426590201, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426591035, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426591664, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426591733, "dur": 1662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426593396, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426593868, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426594107, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426594282, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426594790, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426594899, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426597190, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426597519, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426598330, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426598403, "dur": 5560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426603963, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426604242, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426604302, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426605020, "dur": 3426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426608446, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426608556, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426608646, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426608825, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426608897, "dur": 1536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426610433, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426610904, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426611166, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748622426611516, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426611842, "dur": 98008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426709850, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426711449, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426711726, "dur": 3068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426714795, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426714918, "dur": 4051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426718969, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426719034, "dur": 2716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426721750, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426721804, "dur": 1598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426723403, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426723466, "dur": 3625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426727091, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426727156, "dur": 3115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426730273, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748622426730383, "dur": 5674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748622426736104, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426477929, "dur": 62843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426540776, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426541516, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426541803, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426541920, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426542128, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426542339, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426542476, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426542661, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426542711, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426542798, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426542933, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426543197, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426543327, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426543450, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426543606, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426543730, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426543922, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426543994, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426544199, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426544483, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426544613, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426544803, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426544940, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426545028, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426545138, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426545273, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426545427, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426545591, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426545757, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426545847, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426546077, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426546348, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426546428, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426546674, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426546777, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426546980, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748622426547163, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426547419, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426548049, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748622426548425, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426548842, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426549061, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748622426549224, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426549341, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426549424, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426549607, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426549721, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426550033, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426550104, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748622426550217, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426550321, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426550463, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426550557, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426550611, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426550773, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426551663, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426551716, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426552042, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426552103, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426552279, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426552429, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426552622, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426552799, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426552859, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426552987, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426553122, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426553381, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426553504, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748622426553702, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426553885, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426553987, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426554063, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426555103, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426555712, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426556458, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426557136, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426557941, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426558810, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426559841, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426560814, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426561804, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426562691, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426563790, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426564653, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426565598, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426566619, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426567393, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426568222, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426569237, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426570092, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426570892, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426571929, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426572506, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426573139, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426573964, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426574805, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426575847, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426576749, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426577554, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426578479, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426579690, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426580652, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426581498, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426582399, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426583296, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426584161, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426585093, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426586090, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426586282, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426587498, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426587911, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426587984, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426589492, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426591994, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426592306, "dur": 16141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426608448, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426608738, "dur": 1872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426610611, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426610905, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426611171, "dur": 2144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426613315, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748622426613421, "dur": 96452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426709874, "dur": 2660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426712534, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426712697, "dur": 2543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426715241, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426715335, "dur": 2877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426718212, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426718308, "dur": 4352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426722660, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426722720, "dur": 4253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426726974, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426727051, "dur": 2990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426730086, "dur": 3809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748622426733895, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426734056, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426734193, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426734400, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426734554, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426734691, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426734833, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426734949, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426735069, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748622426735130, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426735240, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426735436, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426735532, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426735645, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426735738, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426736104, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748622426736164, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426477935, "dur": 62867, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426540822, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426541505, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426541764, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426541871, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426541987, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426542236, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426542371, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426542443, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426542592, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426542817, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426542900, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426543053, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426543239, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426543368, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426543505, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426543682, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426543932, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426544117, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426544265, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426544493, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426544595, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426544769, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426544887, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426545044, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426545208, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426545347, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426545491, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426545635, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426545736, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426545879, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426545979, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426546228, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748622426546726, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426546867, "dur": 6513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426553381, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426553584, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426553725, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426555715, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426555774, "dur": 19192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426574967, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426575398, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426575510, "dur": 2568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426578079, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426578139, "dur": 6637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426584777, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426585116, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426585194, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426585272, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426586175, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426586289, "dur": 3555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426589844, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426590330, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426590809, "dur": 1810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426592619, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426593233, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426593396, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426593451, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426593734, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426594200, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426595044, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426596219, "dur": 1020, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426597242, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426599139, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426599356, "dur": 2086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426601442, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426601576, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426601835, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426604231, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426604511, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426604745, "dur": 4272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426609017, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426609313, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426609391, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426609521, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426610972, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426611157, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426611252, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426611639, "dur": 753, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426612420, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426612524, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426612743, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426613303, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748622426613409, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426613684, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426613905, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426614141, "dur": 95760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426709901, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426712571, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426712795, "dur": 3308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426716103, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426716270, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426718839, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426718912, "dur": 2721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426721634, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426721701, "dur": 3594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426725295, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426725415, "dur": 2416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426727831, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426727888, "dur": 2989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426730877, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748622426730944, "dur": 5213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748622426736208, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426477987, "dur": 62840, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426540832, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426541516, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426541835, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426541937, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426542126, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426542384, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426542557, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426542751, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426542987, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426543073, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426543264, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426543395, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426543600, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426543692, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426543868, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426544013, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426544203, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426544483, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426544614, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426544791, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426544955, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426545068, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426545184, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426545322, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426545519, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426545664, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426545898, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426545983, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426546247, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426546445, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426546538, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426546636, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426546913, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748622426547068, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426547135, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748622426547331, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426547496, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748622426547670, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426547757, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426547864, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426548042, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426548192, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426549013, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426549162, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426549271, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748622426549369, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426549446, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426549497, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426549581, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426549823, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426550105, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748622426550202, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426550327, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426550515, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426550794, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426550995, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748622426551055, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426551201, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426551316, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426551711, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426551890, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426551959, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426552022, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426552109, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426552337, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426552411, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426552501, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426552671, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426552866, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426552970, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426553172, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426553406, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426553543, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748622426553595, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426553825, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426553934, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426554010, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426555033, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426555659, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426556393, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426557070, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426557759, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426558622, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426559697, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426560617, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426561663, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426562505, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426563627, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426564520, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426565425, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426566428, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426567207, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426567997, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426568994, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426569843, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426570586, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426571635, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426572431, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426573418, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426574187, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426575041, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426576126, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426577049, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426577959, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426578974, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426579977, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426580978, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426581854, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426582893, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426583785, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426584539, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426585646, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426586217, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426586417, "dur": 2009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426588426, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426588877, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426588939, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426589009, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426589962, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426590121, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426592610, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426593149, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426593220, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426593850, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426594135, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426594289, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426594596, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426594889, "dur": 1993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426596882, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426597326, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426597390, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426597637, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426598585, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426599463, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426599829, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426600097, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426600291, "dur": 2152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426602444, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426602849, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426603090, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426603187, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426603394, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426603623, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426603704, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426603850, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426603908, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426603971, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426604025, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426604568, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426604799, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426606669, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426607070, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426607876, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426608025, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748622426608297, "dur": 1362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426609659, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426609993, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426610122, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748622426610179, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426610443, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426610517, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426610750, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426610906, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426611178, "dur": 98669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426709849, "dur": 4114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426713963, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426714056, "dur": 3682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426717739, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426717863, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426720340, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426720417, "dur": 2391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426722808, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426722959, "dur": 3349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426726308, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426726372, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426729513, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426729626, "dur": 3541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748622426733167, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426733240, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748622426733436, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426733795, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426733876, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426734121, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426734299, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426734398, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426734498, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426734628, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426734757, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426734835, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426735049, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426735226, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426735594, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426735661, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748622426735888, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426477993, "dur": 62846, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426540843, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426541527, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426541807, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426541925, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426542076, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426542383, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426542525, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426542704, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426542791, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426542979, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426543135, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426543297, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426543411, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426543631, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426543794, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426544119, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426544273, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426544522, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426544658, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426544763, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426544949, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426545051, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426545276, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426545381, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426545554, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426545659, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426545767, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426545838, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426546062, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426546126, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748622426546330, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426546478, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426546584, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748622426547297, "dur": 6109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426553406, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426553670, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426553817, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426553940, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426554013, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426554825, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426555521, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426556226, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426556880, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426557560, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426558234, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426559208, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426560123, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426561174, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426562118, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426563138, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426564154, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426565056, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426566006, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426566897, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426567710, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426568622, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426569531, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426570352, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426571541, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426572375, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426573147, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426573969, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426574823, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426575868, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426576769, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426577632, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426578543, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426579700, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426580694, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426581531, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426582490, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426583399, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426584254, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426585520, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426586169, "dur": 9902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426596072, "dur": 1309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426597392, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426597486, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426598123, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426598189, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426600068, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426600339, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426600479, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426600647, "dur": 1855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426602502, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426602690, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426602763, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426602843, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426603313, "dur": 4009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426607323, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426607948, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426608591, "dur": 2893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426611484, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426611697, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426611772, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426612238, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426612383, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748622426612490, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426612718, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426613278, "dur": 96587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426709867, "dur": 3337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426713205, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426713297, "dur": 5797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426719095, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426719162, "dur": 3352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426722515, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426722584, "dur": 2793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426725378, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426725479, "dur": 3587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426729066, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426729129, "dur": 6569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748622426735779, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748622426736211, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426477999, "dur": 62849, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426540852, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426541525, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426541826, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426541966, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426542116, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426542385, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426542592, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426542718, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426542880, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426542946, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426543067, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426543223, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426543330, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426543470, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426543661, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426543785, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426543936, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426544053, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426544234, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426544496, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426544668, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426544777, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426544906, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426545158, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426545285, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426545512, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426545637, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426545888, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426546089, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426546298, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426546392, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426546558, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426546662, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426547149, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426547412, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426547530, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426547667, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426547855, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426547978, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748622426548260, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748622426548582, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426549050, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426549145, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748622426549296, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426549386, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426549594, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426550043, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426550357, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426550610, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748622426550781, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426551064, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426551213, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426551835, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426551901, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426551974, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426552065, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426552148, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426552316, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426552918, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426553313, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426553406, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426553532, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748622426553607, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426553749, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426553803, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426553891, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426553971, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426554889, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426555548, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426556277, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426556952, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426557622, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426558297, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426559286, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426560288, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426561247, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426562109, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426563068, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426564097, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426564998, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426565919, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426566812, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426567622, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426568502, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426569436, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426570279, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426571195, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426572179, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426572953, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426573803, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426574593, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426575551, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426575628, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426575700, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426576665, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426577477, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426578476, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426579486, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426580445, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426581345, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426582260, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426583239, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426584115, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426585009, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426585960, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426586189, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426586479, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426588500, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426588763, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426588839, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426588918, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426589889, "dur": 2208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426592098, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426592450, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_F0EDAB69F288A2F3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426592502, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426592568, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426592964, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426593096, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426593739, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426595707, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426596026, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426596471, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426596573, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426597561, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426597887, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426597953, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426600829, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426600949, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426601587, "dur": 1739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426603326, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426603550, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426603616, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426603823, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426603921, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426603995, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426604086, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426604893, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426605044, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426605304, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426605362, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426606517, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426606618, "dur": 2138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426608756, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426609058, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426609237, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426609299, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426609698, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426609839, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426609957, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426610021, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748622426610076, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426610184, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426610901, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426611175, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748622426611294, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426611667, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426612262, "dur": 97600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426709865, "dur": 3445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426713311, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426713411, "dur": 2703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426716115, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426716201, "dur": 3654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426719856, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426719916, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426722121, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426722283, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426724486, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426724700, "dur": 2573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426727273, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426727338, "dur": 3581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426730919, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748622426731009, "dur": 4775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748622426735882, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426478005, "dur": 62850, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426540857, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426541505, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426541782, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426541964, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426542277, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426542369, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426542456, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426542608, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426542770, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426542891, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426542969, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426543116, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426543230, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426543370, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426543487, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426543854, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426543961, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426544150, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426544430, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426544574, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426544759, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426544879, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426545015, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426545143, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426545290, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426545470, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426545643, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426545753, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426545911, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426546027, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748622426546149, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426546291, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426546449, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426546665, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426546876, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426547295, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426547410, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426547828, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426547960, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748622426548163, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748622426548608, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426548950, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426549294, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426549400, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426549673, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426549756, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426549810, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426550212, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426550376, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426550729, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426550838, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426550968, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748622426551092, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426551203, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426551278, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426551356, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426551758, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426551841, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426551934, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426552008, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426552120, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426552238, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748622426552384, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426552488, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426552681, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426552998, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426553254, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426553395, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426553495, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748622426553602, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426553805, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426553901, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426553988, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426555125, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426555728, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426556447, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426557139, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426558033, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426558946, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426559958, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426561015, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426562026, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426563057, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426564142, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426565140, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426566096, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426566968, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426567746, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426568663, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426569518, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426570377, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426571549, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426572370, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426573222, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426574031, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426574868, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426576084, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426576939, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426577810, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426578901, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426579928, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426580919, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426581775, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426582740, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426583573, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426584363, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426585497, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426586141, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426586274, "dur": 5940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426592214, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426592717, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426593059, "dur": 2069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426595128, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426595537, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748622426595768, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426595824, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426596961, "dur": 698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426597664, "dur": 4335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1748622426602073, "dur": 370, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426708436, "dur": 333, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426602724, "dur": 106056, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1748622426709847, "dur": 3650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426713497, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426713664, "dur": 2128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426715792, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426716013, "dur": 2663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426718677, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426718777, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426721131, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426721192, "dur": 2125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426723317, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426723379, "dur": 2005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426725385, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426725497, "dur": 3012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426728510, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426728662, "dur": 2853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748622426731516, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748622426731618, "dur": 4654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748622426738607, "dur": 670, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 57575, "tid": 15, "ts": 1748622426750673, "dur": 1243, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 57575, "tid": 15, "ts": 1748622426751947, "dur": 1281, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 57575, "tid": 15, "ts": 1748622426747241, "dur": 6617, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}