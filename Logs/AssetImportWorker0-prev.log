Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker0.log
-srvPort
58372
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2355421789 [EditorId] 2355421789 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 14.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56697
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.002752 seconds.
- Loaded All Assemblies, in  0.409 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.316 seconds
Domain Reload Profiling: 725ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (182ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (177ms)
				TypeCache.ScanAssembly (165ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (316ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (277ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (113ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.718 seconds
Refreshing native plugins compatible for Editor in 3.16 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.760 seconds
Domain Reload Profiling: 1479ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (543ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (183ms)
				TypeCache.ScanAssembly (161ms)
			BuildScriptInfoCaches (34ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (761ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (606ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (412ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.24 seconds
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.4 MB). Loaded Objects now: 6813.
Memory consumption went from 166.8 MB to 164.4 MB.
Total: 28.751750 ms (FindLiveObjects: 1.749292 ms CreateObjectMapping: 0.992458 ms MarkObjects: 21.647542 ms  DeleteObjects: 4.361834 ms)

========================================================================
Received Import Request.
  Time since last request: 48732.275873 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fe961e05abf57b67e8be1feeb7bcc699') in 0.460114333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 22.525790 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa62a9db446ed7e89bf8cba4ff561617') in 0.03958525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 5.428164 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f9aa444200bca0445af97bf101cb015') in 0.033184417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fc87000 may have been prematurely finalized
- Loaded All Assemblies, in  0.758 seconds
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.605 seconds
Domain Reload Profiling: 1368ms
	BeginReloadAssembly (275ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (64ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (421ms)
		LoadAssemblies (277ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (157ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (606ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (444ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.9 MB). Loaded Objects now: 6862.
Memory consumption went from 169.6 MB to 165.7 MB.
Total: 6.247125 ms (FindLiveObjects: 0.392375 ms CreateObjectMapping: 0.200250 ms MarkObjects: 4.266334 ms  DeleteObjects: 1.387584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fc87000 may have been prematurely finalized
- Loaded All Assemblies, in  0.629 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.609 seconds
Domain Reload Profiling: 1241ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (226ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.0 MB). Loaded Objects now: 6864.
Memory consumption went from 163.0 MB to 160.0 MB.
Total: 7.021750 ms (FindLiveObjects: 0.433208 ms CreateObjectMapping: 0.211333 ms MarkObjects: 5.027625 ms  DeleteObjects: 1.349208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 110.586693 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20424b982154e3af96dd3dad8a55fc59') in 0.52689175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 4.355999 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20e463f13ed66c71138726b3c14bd5bf') in 0.0543305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 781.202693 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5880e021387e0d45100511179427099b') in 0.125820875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fc87000 may have been prematurely finalized
- Loaded All Assemblies, in  1.550 seconds
Refreshing native plugins compatible for Editor in 13.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.84 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.946 seconds
Domain Reload Profiling: 2506ms
	BeginReloadAssembly (584ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (181ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (156ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (889ms)
		LoadAssemblies (482ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (529ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (442ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (947ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (701ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.3 MB). Loaded Objects now: 6886.
Memory consumption went from 167.4 MB to 164.2 MB.
Total: 9.207334 ms (FindLiveObjects: 0.568209 ms CreateObjectMapping: 0.333916 ms MarkObjects: 6.732875 ms  DeleteObjects: 1.571917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.870 seconds
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 8.57 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.726 seconds
Domain Reload Profiling: 1603ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (164ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (465ms)
		LoadAssemblies (329ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (207ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (730ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.7 MB). Loaded Objects now: 6888.
Memory consumption went from 167.1 MB to 164.3 MB.
Total: 9.373292 ms (FindLiveObjects: 0.472625 ms CreateObjectMapping: 0.414083 ms MarkObjects: 6.937084 ms  DeleteObjects: 1.549084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 93.439553 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8d9f038b46178ca74f0f4214634c415') in 0.335996917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 65.835802 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7fb248d83c616286af0a82a2bc62b6c') in 0.031322292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.758 seconds
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.86 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.773 seconds
Domain Reload Profiling: 1534ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (477ms)
		LoadAssemblies (298ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (773ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (584ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.2 MB). Loaded Objects now: 6910.
Memory consumption went from 171.4 MB to 169.2 MB.
Total: 13.251292 ms (FindLiveObjects: 0.669250 ms CreateObjectMapping: 0.772667 ms MarkObjects: 9.316333 ms  DeleteObjects: 2.492167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 95.504059 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af9055f1b4123701387d1e25bd49e546') in 0.342842542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.674 seconds
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.694 seconds
Domain Reload Profiling: 1371ms
	BeginReloadAssembly (243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (376ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (133ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (695ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (545ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (409ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.0 MB). Loaded Objects now: 6932.
Memory consumption went from 175.6 MB to 172.6 MB.
Total: 10.534375 ms (FindLiveObjects: 0.499458 ms CreateObjectMapping: 0.213000 ms MarkObjects: 7.529917 ms  DeleteObjects: 2.291166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.315 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.770 seconds
Domain Reload Profiling: 2090ms
	BeginReloadAssembly (573ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (57ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (167ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (668ms)
		LoadAssemblies (631ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (771ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (591ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (426ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.8 MB). Loaded Objects now: 6934.
Memory consumption went from 175.2 MB to 172.4 MB.
Total: 11.997292 ms (FindLiveObjects: 0.625167 ms CreateObjectMapping: 0.467666 ms MarkObjects: 9.136375 ms  DeleteObjects: 1.766833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.757 seconds
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.49 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.665 seconds
Domain Reload Profiling: 1426ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (446ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (184ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (665ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (489ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (348ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.6 MB). Loaded Objects now: 6936.
Memory consumption went from 175.2 MB to 172.6 MB.
Total: 11.617166 ms (FindLiveObjects: 0.668625 ms CreateObjectMapping: 0.316083 ms MarkObjects: 9.153250 ms  DeleteObjects: 1.478625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2479.190855 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3eb22ac3c11897cba9463f0d0b85f9e2') in 0.419464125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 247.759520 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6df636b2c6d114a95234cff6ea998588') in 0.046826542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.343 seconds
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.673 seconds
Domain Reload Profiling: 2019ms
	BeginReloadAssembly (609ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (113ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (146ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (639ms)
		LoadAssemblies (694ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (673ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (512ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (387ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.1 MB). Loaded Objects now: 6958.
Memory consumption went from 179.6 MB to 177.5 MB.
Total: 11.128625 ms (FindLiveObjects: 0.452959 ms CreateObjectMapping: 0.265125 ms MarkObjects: 7.073416 ms  DeleteObjects: 3.335833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 749.820526 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc1403de5d65a69e517edddf04407b42') in 0.325955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.270 seconds
Refreshing native plugins compatible for Editor in 3.24 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.782 seconds
Domain Reload Profiling: 2056ms
	BeginReloadAssembly (507ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (69ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (146ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (701ms)
		LoadAssemblies (606ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (283ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (220ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (783ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (579ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (435ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.1 MB). Loaded Objects now: 6980.
Memory consumption went from 183.6 MB to 180.5 MB.
Total: 8.026916 ms (FindLiveObjects: 0.602750 ms CreateObjectMapping: 0.253666 ms MarkObjects: 5.594959 ms  DeleteObjects: 1.574750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 364.721580 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b8d286bdd1dc90b19fb408e68761fa5') in 0.348584625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.887 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.64 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.009 seconds
Domain Reload Profiling: 1901ms
	BeginReloadAssembly (368ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (65ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (191ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (457ms)
		LoadAssemblies (282ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (228ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1010ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (804ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (644ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.5 MB). Loaded Objects now: 7002.
Memory consumption went from 187.7 MB to 185.3 MB.
Total: 17.306917 ms (FindLiveObjects: 0.535791 ms CreateObjectMapping: 0.511334 ms MarkObjects: 14.391500 ms  DeleteObjects: 1.867667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.655 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.665 seconds
Domain Reload Profiling: 1323ms
	BeginReloadAssembly (212ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (386ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (162ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (140ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (665ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.8 MB). Loaded Objects now: 7004.
Memory consumption went from 187.4 MB to 183.5 MB.
Total: 11.068500 ms (FindLiveObjects: 1.570417 ms CreateObjectMapping: 0.279375 ms MarkObjects: 7.206375 ms  DeleteObjects: 2.011125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 72.063326 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4bdf8a9cf98ad749e2d6b9a1f199f9b5') in 0.323530708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.678 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.778 seconds
Domain Reload Profiling: 2460ms
	BeginReloadAssembly (779ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (136ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (264ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (771ms)
		LoadAssemblies (696ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (300ms)
			TypeCache.Refresh (51ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (224ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (573ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (410ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.9 MB). Loaded Objects now: 7026.
Memory consumption went from 191.8 MB to 188.9 MB.
Total: 8.607333 ms (FindLiveObjects: 0.707125 ms CreateObjectMapping: 0.459416 ms MarkObjects: 5.928042 ms  DeleteObjects: 1.512167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 276.373784 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a17fc54baa6a5cfa20673abba4683205') in 0.351870666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.686 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.28 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.672 seconds
Domain Reload Profiling: 1361ms
	BeginReloadAssembly (261ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (367ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (672ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (354ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.2 MB). Loaded Objects now: 7048.
Memory consumption went from 195.9 MB to 193.6 MB.
Total: 21.922583 ms (FindLiveObjects: 3.558166 ms CreateObjectMapping: 1.229334 ms MarkObjects: 15.576208 ms  DeleteObjects: 1.558000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 316.421277 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '06dd08760337bd82b544bb9f4e6a6aa3') in 0.556898417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 1.951507 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a69e3b9f673b6602041db3d9f192ca3') in 0.017767042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 12.513257 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '51f1a7005d0fd1c370b79c30c6a6da9d') in 0.038491042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 2.666617 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '902bbf2f60baa7103f97eb73399a5f3b') in 0.009999875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.250492 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a8bacc79cd26f75a79a7cb05f7932784') in 0.007206292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.834441 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ec6d30cf46894c8baabff0057389454') in 0.008701042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.432139 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67f8e4a1b577dbaa6fc2fa6500cad916') in 0.007420084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.235 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.676 seconds
Domain Reload Profiling: 1916ms
	BeginReloadAssembly (509ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (242ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (656ms)
		LoadAssemblies (521ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (269ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (199ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (677ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (507ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.2 MB). Loaded Objects now: 7070.
Memory consumption went from 199.8 MB to 196.7 MB.
Total: 10.031250 ms (FindLiveObjects: 0.589250 ms CreateObjectMapping: 0.364334 ms MarkObjects: 7.637750 ms  DeleteObjects: 1.438834 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.647 seconds
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.635 seconds
Domain Reload Profiling: 1286ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (400ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (636ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (462ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (322ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.7 MB). Loaded Objects now: 7072.
Memory consumption went from 199.5 MB to 196.8 MB.
Total: 9.732958 ms (FindLiveObjects: 1.000583 ms CreateObjectMapping: 0.278917 ms MarkObjects: 5.826708 ms  DeleteObjects: 2.625959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.134 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.827 seconds
Domain Reload Profiling: 1964ms
	BeginReloadAssembly (483ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (659ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (827ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (655ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (469ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.1 MB). Loaded Objects now: 7074.
Memory consumption went from 199.5 MB to 196.5 MB.
Total: 15.226708 ms (FindLiveObjects: 0.758250 ms CreateObjectMapping: 0.529125 ms MarkObjects: 11.168625 ms  DeleteObjects: 2.770166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.235 seconds
Refreshing native plugins compatible for Editor in 2.56 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 12.23 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.838 seconds
Domain Reload Profiling: 2076ms
	BeginReloadAssembly (681ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (114ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (166ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (488ms)
		LoadAssemblies (541ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (838ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (651ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.4 MB). Loaded Objects now: 7076.
Memory consumption went from 199.5 MB to 197.2 MB.
Total: 12.878125 ms (FindLiveObjects: 0.485000 ms CreateObjectMapping: 0.532375 ms MarkObjects: 10.249083 ms  DeleteObjects: 1.611041 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.060 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.618 seconds
Domain Reload Profiling: 1681ms
	BeginReloadAssembly (522ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (53ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (158ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (448ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (198ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (152ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (618ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (468ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.4 MB). Loaded Objects now: 7078.
Memory consumption went from 199.5 MB to 196.1 MB.
Total: 6.965292 ms (FindLiveObjects: 0.455583 ms CreateObjectMapping: 0.225375 ms MarkObjects: 4.721916 ms  DeleteObjects: 1.561500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.755 seconds
Refreshing native plugins compatible for Editor in 5.49 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.631 seconds
Domain Reload Profiling: 1391ms
	BeginReloadAssembly (264ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (131ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (438ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (632ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (470ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (339ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 6.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.7 MB). Loaded Objects now: 7080.
Memory consumption went from 199.5 MB to 196.8 MB.
Total: 26.480625 ms (FindLiveObjects: 1.765833 ms CreateObjectMapping: 0.843750 ms MarkObjects: 21.504750 ms  DeleteObjects: 2.364750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.222 seconds
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.615 seconds
Domain Reload Profiling: 1840ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (204ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (829ms)
		LoadAssemblies (576ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (304ms)
			TypeCache.Refresh (43ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (246ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (615ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (467ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (331ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (5.3 MB). Loaded Objects now: 7082.
Memory consumption went from 199.5 MB to 194.3 MB.
Total: 8.920375 ms (FindLiveObjects: 0.427709 ms CreateObjectMapping: 0.208333 ms MarkObjects: 5.856000 ms  DeleteObjects: 2.427791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.623 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.649 seconds
Domain Reload Profiling: 1276ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (357ms)
		LoadAssemblies (244ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (650ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (362ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (5.2 MB). Loaded Objects now: 7084.
Memory consumption went from 199.5 MB to 194.3 MB.
Total: 14.733084 ms (FindLiveObjects: 1.651458 ms CreateObjectMapping: 0.280209 ms MarkObjects: 7.968958 ms  DeleteObjects: 4.831709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.798 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.818 seconds
Domain Reload Profiling: 2619ms
	BeginReloadAssembly (993ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (60ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (9ms)
		CreateAndSetChildDomain (293ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (695ms)
		LoadAssemblies (871ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (44ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (218ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (818ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (600ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 3.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.0 MB). Loaded Objects now: 7086.
Memory consumption went from 199.5 MB to 196.5 MB.
Total: 11.165708 ms (FindLiveObjects: 0.750708 ms CreateObjectMapping: 0.458000 ms MarkObjects: 8.110292 ms  DeleteObjects: 1.846250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1555.431368 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '305b222a018ab11345e11ce8103bb494') in 0.37333825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.897 seconds
Refreshing native plugins compatible for Editor in 5.16 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 7.23 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.665 seconds
Domain Reload Profiling: 3571ms
	BeginReloadAssembly (833ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (151ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (345ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (995ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (554ms)
			TypeCache.Refresh (88ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (441ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1668ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1233ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (177ms)
			ProcessInitializeOnLoadAttributes (957ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.9 MB). Loaded Objects now: 7109.
Memory consumption went from 203.9 MB to 201.1 MB.
Total: 43.407917 ms (FindLiveObjects: 4.410458 ms CreateObjectMapping: 0.764500 ms MarkObjects: 32.603792 ms  DeleteObjects: 5.627458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 341.082106 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b13ad155749da79c481d784bc2fc209') in 0.59502325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.544 seconds
Refreshing native plugins compatible for Editor in 3.60 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.64 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.709 seconds
Domain Reload Profiling: 2256ms
	BeginReloadAssembly (671ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (87ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (253ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (798ms)
		LoadAssemblies (758ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (252ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (202ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (533ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 3.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7131.
Memory consumption went from 208.0 MB to 204.4 MB.
Total: 8.677292 ms (FindLiveObjects: 0.470000 ms CreateObjectMapping: 0.286209 ms MarkObjects: 5.246875 ms  DeleteObjects: 2.672958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1046.637150 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cae01e77522e2534fcf75e92dbaacd33') in 0.395308292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.687 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.664 seconds
Domain Reload Profiling: 1354ms
	BeginReloadAssembly (254ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (64ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (377ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (156ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (664ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (507ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7153.
Memory consumption went from 212.1 MB to 208.8 MB.
Total: 12.191750 ms (FindLiveObjects: 0.883167 ms CreateObjectMapping: 0.426416 ms MarkObjects: 7.534125 ms  DeleteObjects: 3.347667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 918.852040 seconds.
  path: Assets/Prefabs/SnakeRiver_Ocean.prefab
  artifactKey: Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SnakeRiver_Ocean.prefab using Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db5f5a2e146caa609c8cfc137ca4df0d') in 0.79015325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.102 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.77 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.884 seconds
Domain Reload Profiling: 1989ms
	BeginReloadAssembly (507ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (78ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (158ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (519ms)
		LoadAssemblies (448ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (235ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (191ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (884ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (649ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (473ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 2.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.4 MB). Loaded Objects now: 7175.
Memory consumption went from 216.1 MB to 213.7 MB.
Total: 44.460875 ms (FindLiveObjects: 7.548417 ms CreateObjectMapping: 0.555958 ms MarkObjects: 31.454125 ms  DeleteObjects: 4.901750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.649 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.650 seconds
Domain Reload Profiling: 1302ms
	BeginReloadAssembly (214ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (148ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (650ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7177.
Memory consumption went from 215.8 MB to 211.6 MB.
Total: 7.167375 ms (FindLiveObjects: 0.468875 ms CreateObjectMapping: 0.279583 ms MarkObjects: 4.566625 ms  DeleteObjects: 1.851708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.795 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.665 seconds
Domain Reload Profiling: 1464ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (454ms)
		LoadAssemblies (335ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (519ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (383ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7179.
Memory consumption went from 215.8 MB to 212.3 MB.
Total: 11.789667 ms (FindLiveObjects: 1.118375 ms CreateObjectMapping: 0.363541 ms MarkObjects: 8.096292 ms  DeleteObjects: 2.210667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.074 seconds
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.85 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.765 seconds
Domain Reload Profiling: 1842ms
	BeginReloadAssembly (466ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (150ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (482ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (209ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (766ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (415ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 7181.
Memory consumption went from 215.7 MB to 212.6 MB.
Total: 13.949583 ms (FindLiveObjects: 0.608708 ms CreateObjectMapping: 0.466750 ms MarkObjects: 10.196208 ms  DeleteObjects: 2.676875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.666 seconds
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.753 seconds
Domain Reload Profiling: 1422ms
	BeginReloadAssembly (209ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (397ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (753ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (584ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (431ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 3.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7183.
Memory consumption went from 215.8 MB to 212.5 MB.
Total: 10.477083 ms (FindLiveObjects: 0.769750 ms CreateObjectMapping: 0.426125 ms MarkObjects: 7.168708 ms  DeleteObjects: 2.111625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.710 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.646 seconds
Domain Reload Profiling: 1359ms
	BeginReloadAssembly (297ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (168ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (362ms)
		LoadAssemblies (233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (167ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (646ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (477ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (334ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.5 MB). Loaded Objects now: 7185.
Memory consumption went from 215.8 MB to 211.3 MB.
Total: 9.495583 ms (FindLiveObjects: 0.588750 ms CreateObjectMapping: 0.241667 ms MarkObjects: 6.253083 ms  DeleteObjects: 2.411042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.213 seconds
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.697 seconds
Domain Reload Profiling: 1913ms
	BeginReloadAssembly (559ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (224ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (596ms)
		LoadAssemblies (454ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (252ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (194ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (697ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (364ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7187.
Memory consumption went from 215.8 MB to 212.5 MB.
Total: 11.529542 ms (FindLiveObjects: 0.995417 ms CreateObjectMapping: 0.486541 ms MarkObjects: 7.829958 ms  DeleteObjects: 2.216667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.653 seconds
Refreshing native plugins compatible for Editor in 6.28 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.97 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.448 seconds
Domain Reload Profiling: 2108ms
	BeginReloadAssembly (214ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (384ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1449ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (938ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (215ms)
			ProcessInitializeOnLoadAttributes (615ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 3.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 7189.
Memory consumption went from 215.8 MB to 213.2 MB.
Total: 22.352625 ms (FindLiveObjects: 1.028542 ms CreateObjectMapping: 0.735250 ms MarkObjects: 18.104750 ms  DeleteObjects: 2.482000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1834.059997 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa4b1d017a93c2449ecf9fe5810d0926') in 0.462042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 19.489765 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa243a9ce2176903828598a760b9f9db') in 0.032167458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 18.584009 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '970e95431c13aa45f1aceddc59c754db') in 0.0519695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 226.685885 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd7511b21a21ddeb63b8845be97e1dc44') in 0.071841958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.1 MB). Loaded Objects now: 7330.
Memory consumption went from 214.8 MB to 211.7 MB.
Total: 70.578917 ms (FindLiveObjects: 1.501708 ms CreateObjectMapping: 0.297167 ms MarkObjects: 65.420833 ms  DeleteObjects: 3.358584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.151 seconds
Refreshing native plugins compatible for Editor in 4.66 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 9.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.880 seconds
Domain Reload Profiling: 2034ms
	BeginReloadAssembly (569ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (130ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (198ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (491ms)
		LoadAssemblies (418ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (218ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (881ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (628ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (450ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 2.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7211.
Memory consumption went from 220.0 MB to 217.0 MB.
Total: 20.044417 ms (FindLiveObjects: 1.074917 ms CreateObjectMapping: 0.496375 ms MarkObjects: 14.934792 ms  DeleteObjects: 3.535333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.681 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.646 seconds
Domain Reload Profiling: 1330ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (402ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (159ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (646ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (459ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (328ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.8 MB). Loaded Objects now: 7213.
Memory consumption went from 219.8 MB to 215.0 MB.
Total: 12.459834 ms (FindLiveObjects: 0.600625 ms CreateObjectMapping: 0.274834 ms MarkObjects: 7.296916 ms  DeleteObjects: 4.286875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.159 seconds
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.794 seconds
Domain Reload Profiling: 1957ms
	BeginReloadAssembly (435ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (133ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (608ms)
		LoadAssemblies (515ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (200ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (794ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (616ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (465ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.8 MB). Loaded Objects now: 7215.
Memory consumption went from 219.8 MB to 217.0 MB.
Total: 16.860750 ms (FindLiveObjects: 0.653666 ms CreateObjectMapping: 0.464542 ms MarkObjects: 12.727083 ms  DeleteObjects: 3.014084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.624 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.620 seconds
Domain Reload Profiling: 1248ms
	BeginReloadAssembly (210ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (357ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (621ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (461ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.1 MB). Loaded Objects now: 7217.
Memory consumption went from 219.8 MB to 214.7 MB.
Total: 10.474875 ms (FindLiveObjects: 0.513875 ms CreateObjectMapping: 1.728667 ms MarkObjects: 5.378292 ms  DeleteObjects: 2.853375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1633.718439 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c357c0c7ffab7234264b019b3eda79f') in 0.780133916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.176 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.791 seconds
Domain Reload Profiling: 1970ms
	BeginReloadAssembly (509ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (39ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (195ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (607ms)
		LoadAssemblies (474ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (336ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (277ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (791ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (603ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (456ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 7239.
Memory consumption went from 224.2 MB to 221.1 MB.
Total: 15.157583 ms (FindLiveObjects: 0.597208 ms CreateObjectMapping: 0.474709 ms MarkObjects: 11.939959 ms  DeleteObjects: 2.144583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 110.953034 seconds.
  path: Assets/Materials/SoulCreature1.mat
  artifactKey: Guid(fe6d92738a30547ea8908f54d5ee931e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/SoulCreature1.mat using Guid(fe6d92738a30547ea8908f54d5ee931e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1761ddbed2903cdb59fd309107a4c7d') in 0.357005958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.728 seconds
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.640 seconds
Domain Reload Profiling: 1371ms
	BeginReloadAssembly (280ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (53ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (156ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (131ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (640ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (358ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.3 MB). Loaded Objects now: 7294.
Memory consumption went from 228.4 MB to 224.1 MB.
Total: 8.585792 ms (FindLiveObjects: 0.541875 ms CreateObjectMapping: 0.284500 ms MarkObjects: 5.499709 ms  DeleteObjects: 2.259417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.640 seconds
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.612 seconds
Domain Reload Profiling: 1256ms
	BeginReloadAssembly (220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (162ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (123ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (613ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (455ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7296.
Memory consumption went from 228.2 MB to 223.9 MB.
Total: 13.264209 ms (FindLiveObjects: 0.515041 ms CreateObjectMapping: 0.303709 ms MarkObjects: 8.462791 ms  DeleteObjects: 3.982042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.653 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.644 seconds
Domain Reload Profiling: 1300ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (391ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (174ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (139ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (644ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7298.
Memory consumption went from 228.2 MB to 224.4 MB.
Total: 11.657000 ms (FindLiveObjects: 1.142875 ms CreateObjectMapping: 0.601791 ms MarkObjects: 7.468750 ms  DeleteObjects: 2.442959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.643 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.647 seconds
Domain Reload Profiling: 1293ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (368ms)
		LoadAssemblies (233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (171ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (647ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (367ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7300.
Memory consumption went from 228.1 MB to 224.8 MB.
Total: 11.687000 ms (FindLiveObjects: 0.458083 ms CreateObjectMapping: 0.227167 ms MarkObjects: 7.587958 ms  DeleteObjects: 3.413125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.625 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.697 seconds
Domain Reload Profiling: 1325ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (375ms)
		LoadAssemblies (233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (697ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (532ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (385ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.5 MB). Loaded Objects now: 7302.
Memory consumption went from 228.1 MB to 223.6 MB.
Total: 13.874958 ms (FindLiveObjects: 1.453917 ms CreateObjectMapping: 0.347291 ms MarkObjects: 8.447500 ms  DeleteObjects: 3.625292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.837 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.885 seconds
Domain Reload Profiling: 1726ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (204ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (440ms)
		LoadAssemblies (269ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (711ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 3.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 7304.
Memory consumption went from 228.1 MB to 224.9 MB.
Total: 16.526417 ms (FindLiveObjects: 0.708708 ms CreateObjectMapping: 1.137000 ms MarkObjects: 12.098458 ms  DeleteObjects: 2.581667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 570.664631 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd1535abd1b1af2eb186fb1754dc64029') in 0.340183667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.654 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.624 seconds
Domain Reload Profiling: 1281ms
	BeginReloadAssembly (242ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (355ms)
		LoadAssemblies (243ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (131ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (625ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (460ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7326.
Memory consumption went from 232.5 MB to 228.6 MB.
Total: 16.307209 ms (FindLiveObjects: 0.994000 ms CreateObjectMapping: 0.497417 ms MarkObjects: 11.738250 ms  DeleteObjects: 3.076375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.715 seconds
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.687 seconds
Domain Reload Profiling: 1406ms
	BeginReloadAssembly (237ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (418ms)
		LoadAssemblies (260ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (688ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (520ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (378ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7328.
Memory consumption went from 232.2 MB to 228.5 MB.
Total: 13.961875 ms (FindLiveObjects: 0.562708 ms CreateObjectMapping: 0.469875 ms MarkObjects: 9.647750 ms  DeleteObjects: 3.280666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.627 seconds
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.83 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.610 seconds
Domain Reload Profiling: 1244ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (246ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (117ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (462ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (335ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7330.
Memory consumption went from 232.2 MB to 228.7 MB.
Total: 10.377083 ms (FindLiveObjects: 0.488000 ms CreateObjectMapping: 0.310416 ms MarkObjects: 6.986042 ms  DeleteObjects: 2.592000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.659 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.655 seconds
Domain Reload Profiling: 1319ms
	BeginReloadAssembly (243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (656ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (468ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 4.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.4 MB). Loaded Objects now: 7332.
Memory consumption went from 232.2 MB to 227.8 MB.
Total: 11.274958 ms (FindLiveObjects: 2.585083 ms CreateObjectMapping: 0.318042 ms MarkObjects: 5.574333 ms  DeleteObjects: 2.797041 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.171 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.70 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.725 seconds
Domain Reload Profiling: 1902ms
	BeginReloadAssembly (486ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (144ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (556ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (276ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (212ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (725ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (554ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (401ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 6.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.1 MB). Loaded Objects now: 7334.
Memory consumption went from 232.1 MB to 230.0 MB.
Total: 63.945666 ms (FindLiveObjects: 12.768208 ms CreateObjectMapping: 0.924042 ms MarkObjects: 41.018709 ms  DeleteObjects: 9.233791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 510.582425 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '568a526389068c95eaecd126e5abc3ef') in 0.526960916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.845 seconds
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.681 seconds
Domain Reload Profiling: 1531ms
	BeginReloadAssembly (339ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (38ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (160ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (446ms)
		LoadAssemblies (309ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (681ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7356.
Memory consumption went from 236.6 MB to 232.4 MB.
Total: 19.697875 ms (FindLiveObjects: 1.256250 ms CreateObjectMapping: 0.414041 ms MarkObjects: 14.302334 ms  DeleteObjects: 3.724083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (4.9 MB). Loaded Objects now: 7356.
Memory consumption went from 227.9 MB to 223.0 MB.
Total: 112.883542 ms (FindLiveObjects: 0.733291 ms CreateObjectMapping: 0.183083 ms MarkObjects: 109.082500 ms  DeleteObjects: 2.883875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.056 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.723 seconds
Domain Reload Profiling: 1783ms
	BeginReloadAssembly (437ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (154ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (551ms)
		LoadAssemblies (488ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (723ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (524ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (380ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.8 MB). Loaded Objects now: 7358.
Memory consumption went from 236.3 MB to 233.4 MB.
Total: 8.087416 ms (FindLiveObjects: 0.661417 ms CreateObjectMapping: 0.336708 ms MarkObjects: 5.192375 ms  DeleteObjects: 1.896250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.663 seconds
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.646 seconds
Domain Reload Profiling: 1312ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (386ms)
		LoadAssemblies (254ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (173ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (646ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (352ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 7360.
Memory consumption went from 236.2 MB to 233.1 MB.
Total: 13.316792 ms (FindLiveObjects: 2.144625 ms CreateObjectMapping: 0.238917 ms MarkObjects: 8.034417 ms  DeleteObjects: 2.897167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1006.301724 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '169ffb96391742aa15c17a2c2d6a5d07') in 0.395604958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 14.924407 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '16e9b4fe6a37aa18b5a5e2c704bf048b') in 0.053759792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.821 seconds
Refreshing native plugins compatible for Editor in 2.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.655 seconds
Domain Reload Profiling: 1480ms
	BeginReloadAssembly (316ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (130ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (442ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (655ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (487ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (354ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 4.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7382.
Memory consumption went from 240.6 MB to 236.5 MB.
Total: 16.426000 ms (FindLiveObjects: 1.203708 ms CreateObjectMapping: 0.534959 ms MarkObjects: 11.053334 ms  DeleteObjects: 3.633166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 726.118959 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce02fa77c3f2b172a1360a3c80ab58fe') in 0.789875375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 5.113271 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a2bf2ed28c4fc71cfb19373989bf47ac') in 0.013698083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.391 seconds
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.76 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.882 seconds
Domain Reload Profiling: 2279ms
	BeginReloadAssembly (659ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (78ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (314ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (635ms)
		LoadAssemblies (504ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (293ms)
			TypeCache.Refresh (45ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (225ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (883ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (687ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (509ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.1 MB). Loaded Objects now: 7404.
Memory consumption went from 244.6 MB to 242.5 MB.
Total: 17.338542 ms (FindLiveObjects: 1.321792 ms CreateObjectMapping: 1.195875 ms MarkObjects: 10.264958 ms  DeleteObjects: 4.554208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.122 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.34 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.846 seconds
Domain Reload Profiling: 1971ms
	BeginReloadAssembly (481ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (184ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (569ms)
		LoadAssemblies (453ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (204ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (846ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (637ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (481ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7406.
Memory consumption went from 244.3 MB to 240.7 MB.
Total: 11.176333 ms (FindLiveObjects: 0.526458 ms CreateObjectMapping: 0.390875 ms MarkObjects: 7.774166 ms  DeleteObjects: 2.484584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.154 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.58 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.871 seconds
Domain Reload Profiling: 2029ms
	BeginReloadAssembly (576ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (5ms)
		CreateAndSetChildDomain (218ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (511ms)
		LoadAssemblies (447ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (871ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (670ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 7408.
Memory consumption went from 244.3 MB to 241.5 MB.
Total: 25.404291 ms (FindLiveObjects: 1.374084 ms CreateObjectMapping: 0.491084 ms MarkObjects: 18.350542 ms  DeleteObjects: 5.185958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.227 seconds
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.074 seconds
Domain Reload Profiling: 2305ms
	BeginReloadAssembly (465ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (62ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (153ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (701ms)
		LoadAssemblies (563ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (249ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1076ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (855ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (126ms)
			ProcessInitializeOnLoadAttributes (667ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7410.
Memory consumption went from 244.3 MB to 240.9 MB.
Total: 33.162250 ms (FindLiveObjects: 0.895417 ms CreateObjectMapping: 0.487166 ms MarkObjects: 29.520625 ms  DeleteObjects: 2.258750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.817 seconds
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.00 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.684 seconds
Domain Reload Profiling: 1505ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (210ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (423ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (223ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (186ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (685ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7412.
Memory consumption went from 244.3 MB to 240.5 MB.
Total: 8.872292 ms (FindLiveObjects: 0.512541 ms CreateObjectMapping: 0.409834 ms MarkObjects: 6.295000 ms  DeleteObjects: 1.654167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.223 seconds
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 8.21 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.299 seconds
Domain Reload Profiling: 2530ms
	BeginReloadAssembly (532ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (59ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (178ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (621ms)
		LoadAssemblies (497ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (209ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1304ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1038ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (836ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 5.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7414.
Memory consumption went from 244.2 MB to 240.4 MB.
Total: 55.110375 ms (FindLiveObjects: 4.226958 ms CreateObjectMapping: 1.227542 ms MarkObjects: 42.332167 ms  DeleteObjects: 7.322542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.872 seconds
Refreshing native plugins compatible for Editor in 3.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.707 seconds
Domain Reload Profiling: 1583ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (5ms)
		CreateAndSetChildDomain (159ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (481ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (233ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (198ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (708ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.4 MB). Loaded Objects now: 7416.
Memory consumption went from 244.3 MB to 241.8 MB.
Total: 16.024584 ms (FindLiveObjects: 0.621666 ms CreateObjectMapping: 0.402292 ms MarkObjects: 9.835125 ms  DeleteObjects: 5.165125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5522.792618 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd1ecd3b020d0397c62ac12911471bff5') in 0.441882208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 8.651112 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e99d1ca88d38f579de0d4cf675cce32d') in 0.043070708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.813 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.88 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.064 seconds
Domain Reload Profiling: 1880ms
	BeginReloadAssembly (290ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (127ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (452ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1064ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (856ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (625ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 5.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 7438.
Memory consumption went from 248.7 MB to 246.0 MB.
Total: 34.985333 ms (FindLiveObjects: 1.695875 ms CreateObjectMapping: 0.610208 ms MarkObjects: 29.687000 ms  DeleteObjects: 2.990166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 75.920927 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '333f2b62e356b6fe0f3fafe00ad673bd') in 0.482217208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 16.482192 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf8b77c25d7698cbac63c931eeaa02b2') in 0.121471375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.421 seconds
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.756 seconds
Domain Reload Profiling: 2182ms
	BeginReloadAssembly (710ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (115ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (246ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (624ms)
		LoadAssemblies (506ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (302ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (238ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (759ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (415ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.3 MB). Loaded Objects now: 7460.
Memory consumption went from 252.7 MB to 248.4 MB.
Total: 9.370666 ms (FindLiveObjects: 0.445958 ms CreateObjectMapping: 0.279875 ms MarkObjects: 6.692625 ms  DeleteObjects: 1.951500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2978.168035 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '306b478c160d4e772b1b03f91c8eb02d') in 0.389847541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 10.465498 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46fb11b3b1a66cc1d2fd020e1bba4486') in 0.050971167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 10.558717 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ef31add855876363ccf1426aeac54c6c') in 0.040487459 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 16.888556 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '26ba7375e6db9d954b1ef4f2fe5f376d') in 0.055272875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.260 seconds
Refreshing native plugins compatible for Editor in 2.39 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.80 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.842 seconds
Domain Reload Profiling: 2108ms
	BeginReloadAssembly (643ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (95ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (219ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (551ms)
		LoadAssemblies (512ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (843ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (636ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.4 MB). Loaded Objects now: 7482.
Memory consumption went from 256.8 MB to 254.4 MB.
Total: 30.036083 ms (FindLiveObjects: 1.897416 ms CreateObjectMapping: 2.460834 ms MarkObjects: 21.393500 ms  DeleteObjects: 4.282208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 142.626445 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a2584141cec45f2f1a0f5ffcaa02124f') in 0.596378917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.168 seconds
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.71 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.958 seconds
Domain Reload Profiling: 2132ms
	BeginReloadAssembly (493ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (65ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (173ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (580ms)
		LoadAssemblies (519ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (959ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (710ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 5.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.3 MB). Loaded Objects now: 7504.
Memory consumption went from 260.8 MB to 258.5 MB.
Total: 74.156084 ms (FindLiveObjects: 18.922000 ms CreateObjectMapping: 1.761292 ms MarkObjects: 45.724917 ms  DeleteObjects: 7.746709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 318.158868 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92db8d87f67aa9a39f079179bcb839d8') in 0.466303291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.868 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.50 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.712 seconds
Domain Reload Profiling: 1584ms
	BeginReloadAssembly (345ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (98ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (136ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (457ms)
		LoadAssemblies (273ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (235ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (712ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (517ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (378ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 3.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7526.
Memory consumption went from 264.8 MB to 261.3 MB.
Total: 27.400708 ms (FindLiveObjects: 1.874000 ms CreateObjectMapping: 2.123667 ms MarkObjects: 20.308500 ms  DeleteObjects: 3.093875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.082 seconds
Refreshing native plugins compatible for Editor in 2.75 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.680 seconds
Domain Reload Profiling: 1765ms
	BeginReloadAssembly (414ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (232ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (589ms)
		LoadAssemblies (432ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (258ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (202ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (680ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (358ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7528.
Memory consumption went from 264.5 MB to 261.5 MB.
Total: 11.483042 ms (FindLiveObjects: 1.433917 ms CreateObjectMapping: 0.434625 ms MarkObjects: 6.858000 ms  DeleteObjects: 2.755625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.471 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.90 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.876 seconds
Domain Reload Profiling: 2351ms
	BeginReloadAssembly (784ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (67ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (454ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (588ms)
		LoadAssemblies (433ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (258ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (226ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (876ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (669ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (482ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 6.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7530.
Memory consumption went from 264.5 MB to 261.5 MB.
Total: 34.508750 ms (FindLiveObjects: 1.584666 ms CreateObjectMapping: 0.537667 ms MarkObjects: 28.801500 ms  DeleteObjects: 3.582750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 140.524883 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '79ff07645fbb070b4335ca3f350beaa9') in 0.424830834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 5.371161 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '754f921e553fc0ca4ee0e1ba764eda01') in 0.049384833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.713 seconds
Refreshing native plugins compatible for Editor in 3.69 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.719 seconds
Domain Reload Profiling: 1435ms
	BeginReloadAssembly (304ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (344ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (173ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (720ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (379ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7552.
Memory consumption went from 268.9 MB to 265.9 MB.
Total: 13.548500 ms (FindLiveObjects: 1.249750 ms CreateObjectMapping: 0.442375 ms MarkObjects: 9.555125 ms  DeleteObjects: 2.300500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.720 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.40 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.741 seconds
Domain Reload Profiling: 1465ms
	BeginReloadAssembly (249ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (127ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (392ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (155ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (742ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (432ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7554.
Memory consumption went from 268.4 MB to 265.2 MB.
Total: 13.114667 ms (FindLiveObjects: 0.596292 ms CreateObjectMapping: 0.567083 ms MarkObjects: 9.303209 ms  DeleteObjects: 2.647208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 102.028133 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae243cfad379e8fdbdf823ba75c4f449') in 0.406063459 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.286 seconds
Refreshing native plugins compatible for Editor in 2.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.823 seconds
Domain Reload Profiling: 2112ms
	BeginReloadAssembly (650ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (78ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (201ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (556ms)
		LoadAssemblies (621ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (205ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (823ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (579ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (117ms)
			ProcessInitializeOnLoadAttributes (411ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 3.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 7576.
Memory consumption went from 272.9 MB to 269.0 MB.
Total: 21.223209 ms (FindLiveObjects: 1.240625 ms CreateObjectMapping: 0.425292 ms MarkObjects: 16.677833 ms  DeleteObjects: 2.878959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.652 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.707 seconds
Domain Reload Profiling: 1362ms
	BeginReloadAssembly (238ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (145ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (355ms)
		LoadAssemblies (240ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (707ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (511ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.1 MB). Loaded Objects now: 7578.
Memory consumption went from 272.5 MB to 267.4 MB.
Total: 9.780250 ms (FindLiveObjects: 0.710250 ms CreateObjectMapping: 0.255542 ms MarkObjects: 5.290458 ms  DeleteObjects: 3.523292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 290.790998 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b14c5a73290a9222965a3fdde01cd9c') in 0.373407583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.0 MB). Loaded Objects now: 7719.
Memory consumption went from 273.1 MB to 270.1 MB.
Total: 46.134208 ms (FindLiveObjects: 1.030417 ms CreateObjectMapping: 0.228250 ms MarkObjects: 42.350166 ms  DeleteObjects: 2.502709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.309 seconds
Refreshing native plugins compatible for Editor in 10.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.51 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.095 seconds
Domain Reload Profiling: 2411ms
	BeginReloadAssembly (535ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (112ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (176ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (696ms)
		LoadAssemblies (556ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (293ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (241ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1096ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (701ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (489ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7600.
Memory consumption went from 276.9 MB to 272.9 MB.
Total: 11.659291 ms (FindLiveObjects: 0.672916 ms CreateObjectMapping: 0.371250 ms MarkObjects: 7.501334 ms  DeleteObjects: 3.113458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.396 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.793 seconds
Domain Reload Profiling: 2196ms
	BeginReloadAssembly (553ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (233ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (758ms)
		LoadAssemblies (574ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (357ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (296ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (793ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (566ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (420ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 7602.
Memory consumption went from 276.6 MB to 273.9 MB.
Total: 16.158083 ms (FindLiveObjects: 1.251791 ms CreateObjectMapping: 1.755875 ms MarkObjects: 11.117958 ms  DeleteObjects: 2.030958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.148 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.864 seconds
Domain Reload Profiling: 2017ms
	BeginReloadAssembly (544ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (152ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (517ms)
		LoadAssemblies (521ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (865ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (674ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (515ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7604.
Memory consumption went from 276.5 MB to 273.1 MB.
Total: 16.142166 ms (FindLiveObjects: 0.584416 ms CreateObjectMapping: 0.398208 ms MarkObjects: 12.658042 ms  DeleteObjects: 2.501083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.784 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.682 seconds
Domain Reload Profiling: 1469ms
	BeginReloadAssembly (308ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (143ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (414ms)
		LoadAssemblies (299ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (682ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (513ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (368ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7606.
Memory consumption went from 276.6 MB to 272.5 MB.
Total: 33.974000 ms (FindLiveObjects: 14.632458 ms CreateObjectMapping: 0.631958 ms MarkObjects: 9.826000 ms  DeleteObjects: 8.882917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.231 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.99 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.852 seconds
Domain Reload Profiling: 2086ms
	BeginReloadAssembly (586ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (258ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (567ms)
		LoadAssemblies (481ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (251ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (206ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (852ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (651ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (477ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7608.
Memory consumption went from 276.6 MB to 273.2 MB.
Total: 11.807000 ms (FindLiveObjects: 1.046375 ms CreateObjectMapping: 1.833458 ms MarkObjects: 6.717875 ms  DeleteObjects: 2.206375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.637 seconds
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.807 seconds
Domain Reload Profiling: 1446ms
	BeginReloadAssembly (235ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (146ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (121ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (807ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (628ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7610.
Memory consumption went from 276.6 MB to 273.2 MB.
Total: 19.385041 ms (FindLiveObjects: 0.559500 ms CreateObjectMapping: 0.314084 ms MarkObjects: 15.959750 ms  DeleteObjects: 2.550875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.134 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.825 seconds
Domain Reload Profiling: 1962ms
	BeginReloadAssembly (522ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (38ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (207ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (544ms)
		LoadAssemblies (457ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (177ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (825ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (604ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (450ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7612.
Memory consumption went from 276.6 MB to 273.1 MB.
Total: 15.666500 ms (FindLiveObjects: 0.537959 ms CreateObjectMapping: 0.436750 ms MarkObjects: 11.578292 ms  DeleteObjects: 3.112875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.920 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.795 seconds
Domain Reload Profiling: 1719ms
	BeginReloadAssembly (364ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (166ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (495ms)
		LoadAssemblies (358ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (186ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (796ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (629ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (464ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7614.
Memory consumption went from 276.5 MB to 273.0 MB.
Total: 12.740333 ms (FindLiveObjects: 0.565584 ms CreateObjectMapping: 0.443833 ms MarkObjects: 9.146208 ms  DeleteObjects: 2.584250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.825 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 11.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.837 seconds
Domain Reload Profiling: 1665ms
	BeginReloadAssembly (328ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (196ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (435ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (223ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (177ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (837ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (642ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 2.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7616.
Memory consumption went from 276.6 MB to 273.2 MB.
Total: 27.594458 ms (FindLiveObjects: 0.501958 ms CreateObjectMapping: 1.006833 ms MarkObjects: 20.887417 ms  DeleteObjects: 5.197166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.737 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.713 seconds
Domain Reload Profiling: 1453ms
	BeginReloadAssembly (250ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (429ms)
		LoadAssemblies (287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (714ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (400ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7618.
Memory consumption went from 276.6 MB to 273.0 MB.
Total: 10.316208 ms (FindLiveObjects: 1.607125 ms CreateObjectMapping: 0.235333 ms MarkObjects: 6.099167 ms  DeleteObjects: 2.373292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2417.030701 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e8346e9aaf32b97d0cc5edb48bbd12df') in 0.456012334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 18.401875 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e87860930b6b804e0a5f33923d51ddff') in 0.066197667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 3.795613 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0aa3677f22b26ccaf32043e5d539ffc6') in 0.038284833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 5.438113 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '97fb4f5b310d14ddd3a708220707cbcd') in 0.013976333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 23.355263 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3ab5b91c91710984d20e56d9a7045a2b') in 0.048485792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 22.332540 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4623fffd169f15316ca8077626018fa9') in 0.049245833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.323 seconds
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.729 seconds
Domain Reload Profiling: 2055ms
	BeginReloadAssembly (685ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (86ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (5ms)
		CreateAndSetChildDomain (285ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (570ms)
		LoadAssemblies (491ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (182ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (729ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (532ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (395ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7640.
Memory consumption went from 281.0 MB to 277.6 MB.
Total: 10.615250 ms (FindLiveObjects: 0.873291 ms CreateObjectMapping: 0.330834 ms MarkObjects: 7.015333 ms  DeleteObjects: 2.395459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 354.381539 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '229ff3eb093e428f01b280e41882be59') in 0.309179917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.638 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.24 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.744 seconds
Domain Reload Profiling: 1384ms
	BeginReloadAssembly (243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (240ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (140ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (121ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (744ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (581ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (427ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.2 MB). Loaded Objects now: 7662.
Memory consumption went from 285.1 MB to 279.9 MB.
Total: 7.399416 ms (FindLiveObjects: 0.411958 ms CreateObjectMapping: 0.288750 ms MarkObjects: 4.626375 ms  DeleteObjects: 2.072000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.473 seconds
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.764 seconds
Domain Reload Profiling: 2242ms
	BeginReloadAssembly (613ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (116ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (213ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (790ms)
		LoadAssemblies (723ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (765ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (586ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (424ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 7664.
Memory consumption went from 284.7 MB to 280.8 MB.
Total: 10.455292 ms (FindLiveObjects: 0.678334 ms CreateObjectMapping: 0.429250 ms MarkObjects: 7.321375 ms  DeleteObjects: 2.025083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1115.772012 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83da5be76da1e5c4f6528e183bfb978d') in 0.315361041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.020 seconds
Refreshing native plugins compatible for Editor in 4.09 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.733 seconds
Domain Reload Profiling: 1756ms
	BeginReloadAssembly (433ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (46ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (243ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (523ms)
		LoadAssemblies (376ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (218ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (733ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (520ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (365ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7686.
Memory consumption went from 289.2 MB to 285.1 MB.
Total: 21.430542 ms (FindLiveObjects: 0.619583 ms CreateObjectMapping: 2.286417 ms MarkObjects: 14.428666 ms  DeleteObjects: 4.094959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.921 seconds
Refreshing native plugins compatible for Editor in 1.78 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.51 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.782 seconds
Domain Reload Profiling: 1706ms
	BeginReloadAssembly (296ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (565ms)
		LoadAssemblies (422ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (198ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (783ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (608ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (441ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7688.
Memory consumption went from 288.8 MB to 285.2 MB.
Total: 15.653583 ms (FindLiveObjects: 0.582792 ms CreateObjectMapping: 0.429625 ms MarkObjects: 11.306708 ms  DeleteObjects: 3.333750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.678 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 1460ms
	BeginReloadAssembly (250ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (143ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (780ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (588ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (428ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7690.
Memory consumption went from 288.8 MB to 284.8 MB.
Total: 10.658458 ms (FindLiveObjects: 1.653208 ms CreateObjectMapping: 0.392583 ms MarkObjects: 6.409042 ms  DeleteObjects: 2.203334 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.374 seconds
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.740 seconds
Domain Reload Profiling: 2117ms
	BeginReloadAssembly (633ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (168ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (662ms)
		LoadAssemblies (639ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (177ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (740ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (404ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7692.
Memory consumption went from 288.8 MB to 284.7 MB.
Total: 8.550459 ms (FindLiveObjects: 0.591334 ms CreateObjectMapping: 0.354708 ms MarkObjects: 5.412708 ms  DeleteObjects: 2.191208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.661 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.704 seconds
Domain Reload Profiling: 1368ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (416ms)
		LoadAssemblies (289ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (543ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (415ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.1 MB). Loaded Objects now: 7694.
Memory consumption went from 288.8 MB to 283.6 MB.
Total: 7.421417 ms (FindLiveObjects: 0.533708 ms CreateObjectMapping: 0.295625 ms MarkObjects: 4.687625 ms  DeleteObjects: 1.903958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.902 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.692 seconds
Domain Reload Profiling: 1597ms
	BeginReloadAssembly (250ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (435ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (692ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (490ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (333ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7696.
Memory consumption went from 288.8 MB to 285.5 MB.
Total: 12.971959 ms (FindLiveObjects: 2.633625 ms CreateObjectMapping: 0.466458 ms MarkObjects: 6.894375 ms  DeleteObjects: 2.976334 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.3 MB). Loaded Objects now: 7696.
Memory consumption went from 281.8 MB to 278.5 MB.
Total: 57.159417 ms (FindLiveObjects: 0.793583 ms CreateObjectMapping: 0.462375 ms MarkObjects: 53.517500 ms  DeleteObjects: 2.384708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.630 seconds
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.641 seconds
Domain Reload Profiling: 2276ms
	BeginReloadAssembly (738ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (433ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (833ms)
		LoadAssemblies (665ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (320ms)
			TypeCache.Refresh (69ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (642ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (473ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (352ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7698.
Memory consumption went from 288.8 MB to 285.4 MB.
Total: 9.520708 ms (FindLiveObjects: 0.508417 ms CreateObjectMapping: 0.240083 ms MarkObjects: 6.041875 ms  DeleteObjects: 2.729791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.637 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.63 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.666 seconds
Domain Reload Profiling: 1306ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (113ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (353ms)
		LoadAssemblies (241ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (123ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (510ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (378ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.6 MB). Loaded Objects now: 7700.
Memory consumption went from 288.8 MB to 284.3 MB.
Total: 10.763791 ms (FindLiveObjects: 0.499291 ms CreateObjectMapping: 0.409292 ms MarkObjects: 7.816917 ms  DeleteObjects: 2.037792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (6.6 MB). Loaded Objects now: 7700.
Memory consumption went from 281.8 MB to 275.2 MB.
Total: 113.570000 ms (FindLiveObjects: 1.064666 ms CreateObjectMapping: 0.252042 ms MarkObjects: 105.632333 ms  DeleteObjects: 6.620083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.378 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.45 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.728 seconds
Domain Reload Profiling: 2109ms
	BeginReloadAssembly (597ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (47ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (209ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (717ms)
		LoadAssemblies (633ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (729ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (557ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7702.
Memory consumption went from 288.8 MB to 285.4 MB.
Total: 9.260917 ms (FindLiveObjects: 0.733083 ms CreateObjectMapping: 0.427500 ms MarkObjects: 5.850083 ms  DeleteObjects: 2.249250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.631 seconds
Refreshing native plugins compatible for Editor in 1.72 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.641 seconds
Domain Reload Profiling: 1274ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (350ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (146ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (125ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (641ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (350ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.3 MB). Loaded Objects now: 7704.
Memory consumption went from 288.8 MB to 283.5 MB.
Total: 7.068583 ms (FindLiveObjects: 0.425375 ms CreateObjectMapping: 0.299625 ms MarkObjects: 4.307666 ms  DeleteObjects: 2.035250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.301 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.37 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.759 seconds
Domain Reload Profiling: 2064ms
	BeginReloadAssembly (688ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (70ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (266ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (501ms)
		LoadAssemblies (468ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (239ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (759ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (549ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (383ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7706.
Memory consumption went from 288.8 MB to 285.0 MB.
Total: 9.150083 ms (FindLiveObjects: 0.440958 ms CreateObjectMapping: 0.362042 ms MarkObjects: 6.300958 ms  DeleteObjects: 2.045625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.672 seconds
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.685 seconds
Domain Reload Profiling: 1360ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (447ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (685ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (530ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.7 MB). Loaded Objects now: 7708.
Memory consumption went from 288.8 MB to 283.1 MB.
Total: 6.766458 ms (FindLiveObjects: 0.400375 ms CreateObjectMapping: 0.156333 ms MarkObjects: 4.081875 ms  DeleteObjects: 2.127167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  3.750 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.736 seconds
Domain Reload Profiling: 4492ms
	BeginReloadAssembly (2920ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (266ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (7ms)
		CreateAndSetChildDomain (845ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (763ms)
		LoadAssemblies (1625ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (285ms)
			TypeCache.Refresh (56ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (199ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (737ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (540ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7710.
Memory consumption went from 288.8 MB to 285.0 MB.
Total: 12.722583 ms (FindLiveObjects: 0.621125 ms CreateObjectMapping: 0.385250 ms MarkObjects: 9.819000 ms  DeleteObjects: 1.896917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.689 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.73 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.755 seconds
Domain Reload Profiling: 1447ms
	BeginReloadAssembly (214ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (413ms)
		LoadAssemblies (280ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (755ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (587ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (438ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.9 MB). Loaded Objects now: 7712.
Memory consumption went from 288.8 MB to 283.9 MB.
Total: 9.129375 ms (FindLiveObjects: 0.506709 ms CreateObjectMapping: 0.673875 ms MarkObjects: 5.802750 ms  DeleteObjects: 2.145084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.481 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.725 seconds
Domain Reload Profiling: 2213ms
	BeginReloadAssembly (573ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (210ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (820ms)
		LoadAssemblies (592ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (350ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (286ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (726ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (528ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (400ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.5 MB). Loaded Objects now: 7714.
Memory consumption went from 288.8 MB to 284.3 MB.
Total: 7.948375 ms (FindLiveObjects: 0.651333 ms CreateObjectMapping: 0.218792 ms MarkObjects: 5.036500 ms  DeleteObjects: 2.041542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1178.399176 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c2de346dbf0bffe02da58a61b76a1413') in 0.927018541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 52.802330 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b3bf3b1079f21e51eebdbde404edf231') in 0.237064125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.420 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.95 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.770 seconds
Domain Reload Profiling: 2194ms
	BeginReloadAssembly (637ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (55ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (281ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (711ms)
		LoadAssemblies (555ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (278ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (771ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (437ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7736.
Memory consumption went from 293.2 MB to 289.5 MB.
Total: 16.346291 ms (FindLiveObjects: 0.472791 ms CreateObjectMapping: 0.600708 ms MarkObjects: 13.135209 ms  DeleteObjects: 2.137208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 81.964624 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0762ea1d8af7f45ff77d1e7a91a96306') in 0.690818958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.068534 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '203d4f29070ca74fc65c7b2b3519fc08') in 0.012559375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 11.942373 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '981a1216498cfd06c0a15cf1525716e8') in 0.045919959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 7.608482 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '733ccf897b64b09f08b365886805ad9e') in 0.012514417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 3.126611 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd76058eff7262e26023947c94689d863') in 0.042871792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.986679 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c30c9a6304d1c1db769775b79a5a1dfd') in 0.013268541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 4.315349 seconds.
  path: Assets/Prefabs/SnakeRiver_Ocean.prefab
  artifactKey: Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SnakeRiver_Ocean.prefab using Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eacb5c933ccf1e3c862a5effd22e5b11') in 0.012973709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.402 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.843 seconds
Domain Reload Profiling: 2249ms
	BeginReloadAssembly (779ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (57ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (353ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (565ms)
		LoadAssemblies (521ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (277ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (229ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (843ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (652ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (455ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7758.
Memory consumption went from 297.3 MB to 294.3 MB.
Total: 16.779792 ms (FindLiveObjects: 0.721625 ms CreateObjectMapping: 0.505125 ms MarkObjects: 12.337292 ms  DeleteObjects: 3.214292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.372 seconds
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.52 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.740 seconds
Domain Reload Profiling: 2117ms
	BeginReloadAssembly (691ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (199ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (612ms)
		LoadAssemblies (700ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (741ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (532ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (375ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.4 MB). Loaded Objects now: 7760.
Memory consumption went from 296.9 MB to 291.5 MB.
Total: 10.023500 ms (FindLiveObjects: 0.558250 ms CreateObjectMapping: 0.376917 ms MarkObjects: 5.197209 ms  DeleteObjects: 3.890375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.320 seconds
Refreshing native plugins compatible for Editor in 10.26 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.879 seconds
Domain Reload Profiling: 2202ms
	BeginReloadAssembly (605ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (71ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (272ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (634ms)
		LoadAssemblies (550ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (214ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (160ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (880ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (636ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (437ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (4ms)
		ExecutionOrderSort2 (2ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 6.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.9 MB). Loaded Objects now: 7762.
Memory consumption went from 296.9 MB to 294.0 MB.
Total: 28.816833 ms (FindLiveObjects: 2.622542 ms CreateObjectMapping: 1.003791 ms MarkObjects: 18.943167 ms  DeleteObjects: 6.246250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.700 seconds
Refreshing native plugins compatible for Editor in 5.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.89 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.028 seconds
Domain Reload Profiling: 2732ms
	BeginReloadAssembly (776ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (52ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (260ms)
	RebuildCommonClasses (81ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (787ms)
		LoadAssemblies (637ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (373ms)
			TypeCache.Refresh (65ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (261ms)
			ResolveRequiredComponents (38ms)
	FinalizeReload (1028ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (707ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (503ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7764.
Memory consumption went from 296.8 MB to 293.4 MB.
Total: 20.538417 ms (FindLiveObjects: 0.544292 ms CreateObjectMapping: 0.479167 ms MarkObjects: 16.388500 ms  DeleteObjects: 3.125917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7018.451418 seconds.
  path: Assets/Prefabs/SnakeRiver_Ocean.prefab
  artifactKey: Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SnakeRiver_Ocean.prefab using Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9425765d9f271a7d4c46775acaa38005') in 0.666317542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  2.019 seconds
Refreshing native plugins compatible for Editor in 10.66 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.39 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.825 seconds
Domain Reload Profiling: 2849ms
	BeginReloadAssembly (1120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (52ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (10ms)
		CreateAndSetChildDomain (676ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (123ms)
	LoadAllAssembliesAndSetupDomain (696ms)
		LoadAssemblies (606ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (319ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (269ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (826ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (631ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 7786.
Memory consumption went from 301.3 MB to 297.4 MB.
Total: 26.111875 ms (FindLiveObjects: 1.522959 ms CreateObjectMapping: 0.409875 ms MarkObjects: 20.382541 ms  DeleteObjects: 3.795625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 45.488004 seconds.
  path: Assets/Prefabs/SoulCreature1_Tutorial.prefab
  artifactKey: Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature1_Tutorial.prefab using Guid(55ad0e00667c74990909fbb286032235) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fbcaa173c494df7c5dd584480e15b73f') in 0.396637416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 95.519891 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9c91fbf5d9cfd80c3aa23b7091f01295') in 0.179186083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.514 seconds
Refreshing native plugins compatible for Editor in 3.10 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.83 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.261 seconds
Domain Reload Profiling: 2781ms
	BeginReloadAssembly (737ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (163ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (230ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (670ms)
		LoadAssemblies (550ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (296ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (231ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1262ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (797ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (610ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 7809.
Memory consumption went from 305.3 MB to 302.8 MB.
Total: 18.050125 ms (FindLiveObjects: 1.115084 ms CreateObjectMapping: 1.012292 ms MarkObjects: 13.611417 ms  DeleteObjects: 2.310625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.969 seconds
Refreshing native plugins compatible for Editor in 5.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.77 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.789 seconds
Domain Reload Profiling: 1761ms
	BeginReloadAssembly (365ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (145ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (440ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (789ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (559ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (418ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 2.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 7811.
Memory consumption went from 305.0 MB to 302.4 MB.
Total: 52.501792 ms (FindLiveObjects: 3.115125 ms CreateObjectMapping: 0.543792 ms MarkObjects: 42.724292 ms  DeleteObjects: 6.117708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.312 seconds
Refreshing native plugins compatible for Editor in 6.75 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.732 seconds
Domain Reload Profiling: 2048ms
	BeginReloadAssembly (637ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (83ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (240ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (611ms)
		LoadAssemblies (536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (732ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (522ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (380ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7813.
Memory consumption went from 305.0 MB to 301.2 MB.
Total: 17.336959 ms (FindLiveObjects: 1.482625 ms CreateObjectMapping: 0.511541 ms MarkObjects: 12.361292 ms  DeleteObjects: 2.980708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.670 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.750 seconds
Domain Reload Profiling: 1423ms
	BeginReloadAssembly (230ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (115ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (274ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (129ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (751ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (572ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (420ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.0 MB). Loaded Objects now: 7815.
Memory consumption went from 304.9 MB to 299.9 MB.
Total: 7.712250 ms (FindLiveObjects: 0.429625 ms CreateObjectMapping: 0.221792 ms MarkObjects: 5.014084 ms  DeleteObjects: 2.046167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.137 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.89 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.874 seconds
Domain Reload Profiling: 2014ms
	BeginReloadAssembly (521ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (154ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (540ms)
		LoadAssemblies (517ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (194ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (875ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (667ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (485ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (2ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 3.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 7817.
Memory consumption went from 305.0 MB to 302.4 MB.
Total: 33.179166 ms (FindLiveObjects: 0.887417 ms CreateObjectMapping: 0.439417 ms MarkObjects: 24.916500 ms  DeleteObjects: 6.934500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1465.061919 seconds.
  path: Assets/Prefabs/SnakeRiver_Ocean.prefab
  artifactKey: Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SnakeRiver_Ocean.prefab using Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f4d945a9b8ef2a5a284ed3282446c935') in 0.483337458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 17.236498 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fe16ffc9b3bb66b1cbed5f32a6e1993e') in 0.132847083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 9.206449 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'deacdc06f3b8c5ed7ff17287c0173227') in 0.056321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 47.180967 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '186d49a73693f3478c3562ae51a31ad8') in 0.300753959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.516 seconds
Refreshing native plugins compatible for Editor in 6.17 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.46 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.272 seconds
Domain Reload Profiling: 2793ms
	BeginReloadAssembly (730ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (145ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (225ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (688ms)
		LoadAssemblies (517ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (332ms)
			TypeCache.Refresh (45ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (268ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1272ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (952ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (726ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 6.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 7839.
Memory consumption went from 309.4 MB to 306.8 MB.
Total: 53.215500 ms (FindLiveObjects: 8.744583 ms CreateObjectMapping: 2.582709 ms MarkObjects: 38.074333 ms  DeleteObjects: 3.812750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 124.005643 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae8b1a53dd700573c357cbefb9d2bd2d') in 0.454664167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.121 seconds
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.11 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.061 seconds
Domain Reload Profiling: 2185ms
	BeginReloadAssembly (535ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (285ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (509ms)
		LoadAssemblies (451ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1062ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (842ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (649ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7861.
Memory consumption went from 313.5 MB to 310.1 MB.
Total: 23.467209 ms (FindLiveObjects: 1.786042 ms CreateObjectMapping: 0.610292 ms MarkObjects: 17.515291 ms  DeleteObjects: 3.554709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.622 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.74 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.817 seconds
Domain Reload Profiling: 2443ms
	BeginReloadAssembly (886ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (100ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (325ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (669ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (218ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (818ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.3 MB). Loaded Objects now: 7863.
Memory consumption went from 313.1 MB to 310.8 MB.
Total: 18.103292 ms (FindLiveObjects: 0.595584 ms CreateObjectMapping: 0.480959 ms MarkObjects: 13.286708 ms  DeleteObjects: 3.738875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.918 seconds
Refreshing native plugins compatible for Editor in 2.51 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.81 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.771 seconds
Domain Reload Profiling: 1693ms
	BeginReloadAssembly (364ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (206ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (489ms)
		LoadAssemblies (340ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (222ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (772ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (587ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (446ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7865.
Memory consumption went from 313.1 MB to 310.1 MB.
Total: 10.529333 ms (FindLiveObjects: 0.500333 ms CreateObjectMapping: 0.352417 ms MarkObjects: 7.781625 ms  DeleteObjects: 1.894500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.273 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.799 seconds
Domain Reload Profiling: 2076ms
	BeginReloadAssembly (525ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (165ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (658ms)
		LoadAssemblies (580ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (250ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (207ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (800ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (585ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (103ms)
			ProcessInitializeOnLoadAttributes (428ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 5.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 7867.
Memory consumption went from 313.1 MB to 310.0 MB.
Total: 30.264417 ms (FindLiveObjects: 2.281458 ms CreateObjectMapping: 0.831875 ms MarkObjects: 24.245083 ms  DeleteObjects: 2.904750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.700 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.734 seconds
Domain Reload Profiling: 1438ms
	BeginReloadAssembly (234ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (735ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (553ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (406ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 3.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.3 MB). Loaded Objects now: 7869.
Memory consumption went from 313.1 MB to 307.8 MB.
Total: 8.215625 ms (FindLiveObjects: 0.603416 ms CreateObjectMapping: 0.298667 ms MarkObjects: 5.203750 ms  DeleteObjects: 2.109125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.181 seconds
Refreshing native plugins compatible for Editor in 2.43 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.955 seconds
Domain Reload Profiling: 2141ms
	BeginReloadAssembly (561ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (181ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (550ms)
		LoadAssemblies (500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (201ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (956ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (742ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (572ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 3.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7871.
Memory consumption went from 313.1 MB to 310.2 MB.
Total: 30.383709 ms (FindLiveObjects: 1.486833 ms CreateObjectMapping: 1.623750 ms MarkObjects: 24.681042 ms  DeleteObjects: 2.591667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.732 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.774 seconds
Domain Reload Profiling: 1509ms
	BeginReloadAssembly (263ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (133ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (315ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (774ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (438ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.0 MB). Loaded Objects now: 7873.
Memory consumption went from 313.1 MB to 308.1 MB.
Total: 7.646084 ms (FindLiveObjects: 0.441167 ms CreateObjectMapping: 0.333583 ms MarkObjects: 4.986750 ms  DeleteObjects: 1.883709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.175 seconds
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.80 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.891 seconds
Domain Reload Profiling: 2071ms
	BeginReloadAssembly (528ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (149ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (576ms)
		LoadAssemblies (521ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (59ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (683ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7875.
Memory consumption went from 313.1 MB to 309.9 MB.
Total: 15.114542 ms (FindLiveObjects: 0.964125 ms CreateObjectMapping: 0.433083 ms MarkObjects: 11.335750 ms  DeleteObjects: 2.380334 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.738 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.716 seconds
Domain Reload Profiling: 1456ms
	BeginReloadAssembly (257ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (140ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (415ms)
		LoadAssemblies (270ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (716ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (542ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (386ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.8 MB). Loaded Objects now: 7877.
Memory consumption went from 313.1 MB to 308.4 MB.
Total: 8.387416 ms (FindLiveObjects: 0.528042 ms CreateObjectMapping: 0.278416 ms MarkObjects: 5.304292 ms  DeleteObjects: 2.275291 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.161 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.61 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.923 seconds
Domain Reload Profiling: 2089ms
	BeginReloadAssembly (550ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (76ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (181ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (536ms)
		LoadAssemblies (443ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (230ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (191ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (923ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (714ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (131ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 2.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 7879.
Memory consumption went from 313.1 MB to 310.5 MB.
Total: 29.919875 ms (FindLiveObjects: 0.925416 ms CreateObjectMapping: 0.499625 ms MarkObjects: 25.500500 ms  DeleteObjects: 2.993083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.631 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.749 seconds
Domain Reload Profiling: 1383ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (370ms)
		LoadAssemblies (260ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (155ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (127ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (749ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (420ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.8 MB). Loaded Objects now: 7881.
Memory consumption went from 313.1 MB to 308.4 MB.
Total: 6.806208 ms (FindLiveObjects: 0.434708 ms CreateObjectMapping: 0.160458 ms MarkObjects: 4.449542 ms  DeleteObjects: 1.760500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (5.6 MB). Loaded Objects now: 7881.
Memory consumption went from 306.7 MB to 301.1 MB.
Total: 61.509875 ms (FindLiveObjects: 6.736959 ms CreateObjectMapping: 0.606958 ms MarkObjects: 48.084667 ms  DeleteObjects: 6.079917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 22.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.9 MB). Loaded Objects now: 7881.
Memory consumption went from 306.7 MB to 302.9 MB.
Total: 75.006667 ms (FindLiveObjects: 1.083750 ms CreateObjectMapping: 0.831084 ms MarkObjects: 70.313041 ms  DeleteObjects: 2.777833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.220 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.775 seconds
Domain Reload Profiling: 2000ms
	BeginReloadAssembly (501ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (181ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (632ms)
		LoadAssemblies (566ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (776ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (563ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (405ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (37ms)
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 7883.
Memory consumption went from 313.1 MB to 310.6 MB.
Total: 37.237583 ms (FindLiveObjects: 1.685833 ms CreateObjectMapping: 0.442875 ms MarkObjects: 30.243917 ms  DeleteObjects: 4.864583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.798 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.59 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.726 seconds
Domain Reload Profiling: 1528ms
	BeginReloadAssembly (255ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (121ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (295ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (182ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (726ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (543ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.3 MB). Loaded Objects now: 7885.
Memory consumption went from 313.1 MB to 308.8 MB.
Total: 11.188750 ms (FindLiveObjects: 0.728667 ms CreateObjectMapping: 0.459291 ms MarkObjects: 7.912583 ms  DeleteObjects: 2.087334 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.196 seconds
Refreshing native plugins compatible for Editor in 3.28 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.882 seconds
Domain Reload Profiling: 2081ms
	BeginReloadAssembly (584ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (213ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (534ms)
		LoadAssemblies (502ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (203ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (882ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (661ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (495ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (37ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 7887.
Memory consumption went from 313.2 MB to 310.5 MB.
Total: 34.859292 ms (FindLiveObjects: 6.515208 ms CreateObjectMapping: 0.620458 ms MarkObjects: 22.711625 ms  DeleteObjects: 5.011084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.731 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.694 seconds
Domain Reload Profiling: 1428ms
	BeginReloadAssembly (271ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (142ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (399ms)
		LoadAssemblies (252ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (694ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (522ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7889.
Memory consumption went from 313.2 MB to 308.9 MB.
Total: 11.033041 ms (FindLiveObjects: 1.514666 ms CreateObjectMapping: 0.236292 ms MarkObjects: 6.151208 ms  DeleteObjects: 3.129708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (2.5 MB). Loaded Objects now: 7889.
Memory consumption went from 306.7 MB to 304.3 MB.
Total: 90.191417 ms (FindLiveObjects: 1.992125 ms CreateObjectMapping: 0.370250 ms MarkObjects: 75.928875 ms  DeleteObjects: 11.889833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.780 seconds
Refreshing native plugins compatible for Editor in 11.23 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 7.52 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.618 seconds
Domain Reload Profiling: 3407ms
	BeginReloadAssembly (940ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (109ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (373ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (746ms)
		LoadAssemblies (619ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (330ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (266ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1622ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1300ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (1017ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (2ms)
		AwakeInstancesAfterBackupRestoration (61ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 7891.
Memory consumption went from 313.2 MB to 310.5 MB.
Total: 22.709250 ms (FindLiveObjects: 1.101458 ms CreateObjectMapping: 0.634375 ms MarkObjects: 17.770958 ms  DeleteObjects: 3.201209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.4 MB). Loaded Objects now: 7891.
Memory consumption went from 306.7 MB to 303.3 MB.
Total: 65.848416 ms (FindLiveObjects: 3.262000 ms CreateObjectMapping: 0.914500 ms MarkObjects: 56.167834 ms  DeleteObjects: 5.502750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  2.541 seconds
Refreshing native plugins compatible for Editor in 17.30 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 13.31 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.678 seconds
Domain Reload Profiling: 4230ms
	BeginReloadAssembly (1349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (70ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (760ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (1041ms)
		LoadAssemblies (926ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (425ms)
			TypeCache.Refresh (62ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (324ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1681ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (201ms)
			ProcessInitializeOnLoadAttributes (982ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 3.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7893.
Memory consumption went from 313.2 MB to 309.4 MB.
Total: 41.619292 ms (FindLiveObjects: 1.497875 ms CreateObjectMapping: 3.320583 ms MarkObjects: 30.521958 ms  DeleteObjects: 6.277042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.325 seconds
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.62 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.878 seconds
Domain Reload Profiling: 2206ms
	BeginReloadAssembly (666ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (88ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (266ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (585ms)
		LoadAssemblies (506ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (249ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (184ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (878ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (655ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7895.
Memory consumption went from 313.1 MB to 309.0 MB.
Total: 26.064875 ms (FindLiveObjects: 1.132500 ms CreateObjectMapping: 0.438666 ms MarkObjects: 21.048625 ms  DeleteObjects: 3.439917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.659 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.722 seconds
Domain Reload Profiling: 1386ms
	BeginReloadAssembly (226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (374ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (147ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (724ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (533ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (384ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.5 MB). Loaded Objects now: 7897.
Memory consumption went from 313.2 MB to 308.7 MB.
Total: 10.417458 ms (FindLiveObjects: 0.554667 ms CreateObjectMapping: 0.245083 ms MarkObjects: 6.657833 ms  DeleteObjects: 2.958625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.590 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.876 seconds
Domain Reload Profiling: 2470ms
	BeginReloadAssembly (667ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (279ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (806ms)
		LoadAssemblies (711ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (876ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (604ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (103ms)
			ProcessInitializeOnLoadAttributes (445ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7899.
Memory consumption went from 313.2 MB to 309.7 MB.
Total: 21.467959 ms (FindLiveObjects: 1.088333 ms CreateObjectMapping: 0.375917 ms MarkObjects: 17.698458 ms  DeleteObjects: 2.304958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.336 seconds
Refreshing native plugins compatible for Editor in 11.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 7.34 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.288 seconds
Domain Reload Profiling: 2628ms
	BeginReloadAssembly (482ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (245ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (752ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (267ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (232ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1289ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (969ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (711ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (51ms)
Refreshing native plugins compatible for Editor in 5.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.1 MB). Loaded Objects now: 7901.
Memory consumption went from 313.2 MB to 311.1 MB.
Total: 24.323666 ms (FindLiveObjects: 1.307000 ms CreateObjectMapping: 0.988750 ms MarkObjects: 19.380042 ms  DeleteObjects: 2.646750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.706 seconds
Refreshing native plugins compatible for Editor in 2.42 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.687 seconds
Domain Reload Profiling: 2397ms
	BeginReloadAssembly (829ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (75ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (6ms)
		CreateAndSetChildDomain (295ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (778ms)
		LoadAssemblies (825ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (239ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (688ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (489ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7903.
Memory consumption went from 313.2 MB to 309.1 MB.
Total: 8.987458 ms (FindLiveObjects: 0.773750 ms CreateObjectMapping: 0.209458 ms MarkObjects: 6.011250 ms  DeleteObjects: 1.992541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.859 seconds
Refreshing native plugins compatible for Editor in 2.42 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.22 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.857 seconds
Domain Reload Profiling: 1719ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (509ms)
		LoadAssemblies (348ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (857ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (665ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (125ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 7905.
Memory consumption went from 313.1 MB to 309.2 MB.
Total: 14.486791 ms (FindLiveObjects: 0.625416 ms CreateObjectMapping: 3.636834 ms MarkObjects: 8.046583 ms  DeleteObjects: 2.177833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4260.920322 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a7f72d1a7d4cb693ad3d417d31dc508') in 0.464214917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.764 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.47 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.889 seconds
Domain Reload Profiling: 2657ms
	BeginReloadAssembly (960ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (168ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (382ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (718ms)
		LoadAssemblies (684ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (201ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (889ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (662ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7927.
Memory consumption went from 317.6 MB to 314.4 MB.
Total: 26.357917 ms (FindLiveObjects: 1.476833 ms CreateObjectMapping: 0.419792 ms MarkObjects: 20.951542 ms  DeleteObjects: 3.508292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 108.341409 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7dce49a8af11b78fb45f94f6190a687f') in 0.414838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 27.279225 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b6f57fd01550d889f961e06754c2fc4b') in 0.046927375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.481 seconds
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.89 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.932 seconds
Domain Reload Profiling: 2416ms
	BeginReloadAssembly (805ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (78ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (322ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (603ms)
		LoadAssemblies (590ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (269ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (215ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (933ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (697ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (524ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 4.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7949.
Memory consumption went from 321.7 MB to 318.4 MB.
Total: 32.386459 ms (FindLiveObjects: 1.136792 ms CreateObjectMapping: 1.858250 ms MarkObjects: 26.890333 ms  DeleteObjects: 2.499667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 241.074685 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8cf22f2062b1a42f12bd3cd9fa604bfa') in 0.396476584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

