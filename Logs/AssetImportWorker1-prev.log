Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker1.log
-srvPort
58372
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2371703155 [EditorId] 2371703155 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 14.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56699
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.002752 seconds.
- Loaded All Assemblies, in  0.408 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.317 seconds
Domain Reload Profiling: 725ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (181ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (176ms)
				TypeCache.ScanAssembly (164ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (317ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (277ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (114ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.717 seconds
Refreshing native plugins compatible for Editor in 2.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.758 seconds
Domain Reload Profiling: 1476ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (543ms)
		LoadAssemblies (356ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (226ms)
			TypeCache.Refresh (181ms)
				TypeCache.ScanAssembly (159ms)
			BuildScriptInfoCaches (34ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (759ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (604ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (413ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.24 seconds
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 6813.
Memory consumption went from 166.9 MB to 164.3 MB.
Total: 26.765333 ms (FindLiveObjects: 1.767917 ms CreateObjectMapping: 0.519375 ms MarkObjects: 21.989292 ms  DeleteObjects: 2.488125 ms)

========================================================================
Received Import Request.
  Time since last request: 48732.274696 seconds.
  path: Assets/Prefabs/SnakeRiver_Ocean.prefab
  artifactKey: Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SnakeRiver_Ocean.prefab using Guid(07cea402be77c45289bb2ce7f034317b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '510a1609d6674149092a14d34da3caa7') in 0.457736375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e7eb000 may have been prematurely finalized
- Loaded All Assemblies, in  0.761 seconds
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 1363ms
	BeginReloadAssembly (277ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (65ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (422ms)
		LoadAssemblies (283ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (157ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (600ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (443ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.7 MB). Loaded Objects now: 6861.
Memory consumption went from 169.7 MB to 166.0 MB.
Total: 6.328166 ms (FindLiveObjects: 0.395083 ms CreateObjectMapping: 0.202167 ms MarkObjects: 4.265708 ms  DeleteObjects: 1.464166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.630 seconds
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 1233ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (377ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (600ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (466ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (344ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.7 MB). Loaded Objects now: 6863.
Memory consumption went from 163.2 MB to 159.4 MB.
Total: 7.141209 ms (FindLiveObjects: 0.458417 ms CreateObjectMapping: 0.242500 ms MarkObjects: 4.852250 ms  DeleteObjects: 1.587000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.443 seconds
Refreshing native plugins compatible for Editor in 11.16 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.952 seconds
Domain Reload Profiling: 2401ms
	BeginReloadAssembly (482ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (60ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (134ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (880ms)
		LoadAssemblies (460ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (541ms)
			TypeCache.Refresh (83ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (424ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (952ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (704ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (536ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.7 MB). Loaded Objects now: 6865.
Memory consumption went from 163.2 MB to 159.5 MB.
Total: 8.811083 ms (FindLiveObjects: 0.644000 ms CreateObjectMapping: 0.349792 ms MarkObjects: 6.120291 ms  DeleteObjects: 1.696000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.869 seconds
Refreshing native plugins compatible for Editor in 1.78 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.37 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.731 seconds
Domain Reload Profiling: 1605ms
	BeginReloadAssembly (328ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (166ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (459ms)
		LoadAssemblies (318ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (207ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (734ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (568ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (423ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.9 MB). Loaded Objects now: 6867.
Memory consumption went from 163.2 MB to 160.3 MB.
Total: 9.548333 ms (FindLiveObjects: 0.484375 ms CreateObjectMapping: 0.431792 ms MarkObjects: 6.924250 ms  DeleteObjects: 1.706917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.765 seconds
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.791 seconds
Domain Reload Profiling: 1558ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (505ms)
		LoadAssemblies (361ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (200ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (791ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (601ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (450ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.2 MB). Loaded Objects now: 6869.
Memory consumption went from 163.0 MB to 160.8 MB.
Total: 13.736708 ms (FindLiveObjects: 0.707292 ms CreateObjectMapping: 0.670166 ms MarkObjects: 10.271834 ms  DeleteObjects: 2.086916 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e2c3000 may have been prematurely finalized
- Loaded All Assemblies, in  0.668 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.670 seconds
Domain Reload Profiling: 1342ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (398ms)
		LoadAssemblies (202ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (231ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (530ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.0 MB). Loaded Objects now: 6871.
Memory consumption went from 163.0 MB to 160.0 MB.
Total: 10.538541 ms (FindLiveObjects: 0.426417 ms CreateObjectMapping: 0.212750 ms MarkObjects: 7.298584 ms  DeleteObjects: 2.600250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e2c3000 may have been prematurely finalized
- Loaded All Assemblies, in  1.331 seconds
Refreshing native plugins compatible for Editor in 3.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.54 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.769 seconds
Domain Reload Profiling: 2103ms
	BeginReloadAssembly (590ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (158ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (666ms)
		LoadAssemblies (660ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (207ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (769ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (427ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.8 MB). Loaded Objects now: 6873.
Memory consumption went from 163.0 MB to 160.2 MB.
Total: 12.533042 ms (FindLiveObjects: 0.629375 ms CreateObjectMapping: 0.472625 ms MarkObjects: 9.696000 ms  DeleteObjects: 1.734584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e2cb000 may have been prematurely finalized
- Loaded All Assemblies, in  0.739 seconds
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.33 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.652 seconds
Domain Reload Profiling: 1395ms
	BeginReloadAssembly (236ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (445ms)
		LoadAssemblies (267ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (226ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.2 MB). Loaded Objects now: 6875.
Memory consumption went from 163.0 MB to 159.9 MB.
Total: 10.662083 ms (FindLiveObjects: 0.664125 ms CreateObjectMapping: 0.338208 ms MarkObjects: 8.095167 ms  DeleteObjects: 1.563500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e3cb000 may have been prematurely finalized
- Loaded All Assemblies, in  1.307 seconds
Refreshing native plugins compatible for Editor in 2.51 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.679 seconds
Domain Reload Profiling: 1989ms
	BeginReloadAssembly (574ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (141ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (635ms)
		LoadAssemblies (676ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (202ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (679ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (393ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.1 MB). Loaded Objects now: 6877.
Memory consumption went from 163.0 MB to 161.0 MB.
Total: 8.071375 ms (FindLiveObjects: 0.451875 ms CreateObjectMapping: 0.241417 ms MarkObjects: 5.703875 ms  DeleteObjects: 1.672917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17eac7000 may have been prematurely finalized
- Loaded All Assemblies, in  1.231 seconds
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.21 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.793 seconds
Domain Reload Profiling: 2028ms
	BeginReloadAssembly (468ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (136ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (700ms)
		LoadAssemblies (594ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (294ms)
			TypeCache.Refresh (51ms)
				TypeCache.ScanAssembly (10ms)
			BuildScriptInfoCaches (222ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (794ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (599ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (454ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.8 MB). Loaded Objects now: 6879.
Memory consumption went from 163.0 MB to 160.2 MB.
Total: 8.136958 ms (FindLiveObjects: 0.600250 ms CreateObjectMapping: 0.441041 ms MarkObjects: 5.414500 ms  DeleteObjects: 1.680375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e717000 may have been prematurely finalized
- Loaded All Assemblies, in  0.889 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.95 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.987 seconds
Domain Reload Profiling: 1881ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (183ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (485ms)
		LoadAssemblies (288ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (988ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (797ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (636ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.4 MB). Loaded Objects now: 6881.
Memory consumption went from 163.1 MB to 160.6 MB.
Total: 17.111791 ms (FindLiveObjects: 0.581500 ms CreateObjectMapping: 0.498042 ms MarkObjects: 14.095875 ms  DeleteObjects: 1.935708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e60b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.647 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.647 seconds
Domain Reload Profiling: 1297ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (385ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (140ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (647ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.5 MB). Loaded Objects now: 6883.
Memory consumption went from 163.0 MB to 159.6 MB.
Total: 10.466125 ms (FindLiveObjects: 1.601459 ms CreateObjectMapping: 0.225833 ms MarkObjects: 6.381625 ms  DeleteObjects: 2.256125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17eac7000 may have been prematurely finalized
- Loaded All Assemblies, in  1.703 seconds
Refreshing native plugins compatible for Editor in 2.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.757 seconds
Domain Reload Profiling: 2463ms
	BeginReloadAssembly (801ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (80ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (238ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (768ms)
		LoadAssemblies (737ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (294ms)
			TypeCache.Refresh (66ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (200ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (758ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (406ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.2 MB). Loaded Objects now: 6885.
Memory consumption went from 163.1 MB to 159.9 MB.
Total: 8.971250 ms (FindLiveObjects: 0.604667 ms CreateObjectMapping: 0.478792 ms MarkObjects: 5.843291 ms  DeleteObjects: 2.043125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e38b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.657 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 7.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.670 seconds
Domain Reload Profiling: 1329ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (382ms)
		LoadAssemblies (236ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (159ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (364ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (4.2 MB). Loaded Objects now: 6887.
Memory consumption went from 163.1 MB to 158.8 MB.
Total: 23.537875 ms (FindLiveObjects: 6.321458 ms CreateObjectMapping: 0.509500 ms MarkObjects: 13.048333 ms  DeleteObjects: 3.657083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.056 seconds
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.681 seconds
Domain Reload Profiling: 1740ms
	BeginReloadAssembly (390ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (146ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (597ms)
		LoadAssemblies (502ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (222ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (681ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (518ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (375ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.4 MB). Loaded Objects now: 6889.
Memory consumption went from 163.0 MB to 159.6 MB.
Total: 9.825083 ms (FindLiveObjects: 0.629916 ms CreateObjectMapping: 0.846250 ms MarkObjects: 6.682333 ms  DeleteObjects: 1.665458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.637 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.630 seconds
Domain Reload Profiling: 1271ms
	BeginReloadAssembly (193ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (388ms)
		LoadAssemblies (250ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (630ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (461ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.6 MB). Loaded Objects now: 6891.
Memory consumption went from 163.1 MB to 159.5 MB.
Total: 12.090791 ms (FindLiveObjects: 1.812875 ms CreateObjectMapping: 0.460042 ms MarkObjects: 8.257750 ms  DeleteObjects: 1.559084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.149 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.819 seconds
Domain Reload Profiling: 1971ms
	BeginReloadAssembly (496ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (653ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (819ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (479ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.6 MB). Loaded Objects now: 6893.
Memory consumption went from 163.1 MB to 160.5 MB.
Total: 13.255000 ms (FindLiveObjects: 1.304167 ms CreateObjectMapping: 0.943959 ms MarkObjects: 9.243500 ms  DeleteObjects: 1.761875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 6546.667001 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80e2422c97797a53d4e59db961940008') in 0.3587365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.233 seconds
Refreshing native plugins compatible for Editor in 3.10 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 12.54 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.826 seconds
Domain Reload Profiling: 2062ms
	BeginReloadAssembly (679ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (66ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (249ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (490ms)
		LoadAssemblies (522ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (827ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (650ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (488ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.8 MB). Loaded Objects now: 6916.
Memory consumption went from 167.5 MB to 164.7 MB.
Total: 12.151083 ms (FindLiveObjects: 0.498250 ms CreateObjectMapping: 0.515709 ms MarkObjects: 9.212375 ms  DeleteObjects: 1.924167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.070 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.601 seconds
Domain Reload Profiling: 1673ms
	BeginReloadAssembly (518ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (47ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (167ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (474ms)
		LoadAssemblies (482ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (601ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (458ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (336ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.2 MB). Loaded Objects now: 6918.
Memory consumption went from 167.1 MB to 163.9 MB.
Total: 7.212500 ms (FindLiveObjects: 0.434167 ms CreateObjectMapping: 0.219292 ms MarkObjects: 4.866208 ms  DeleteObjects: 1.692541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.779 seconds
Refreshing native plugins compatible for Editor in 5.31 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.620 seconds
Domain Reload Profiling: 1403ms
	BeginReloadAssembly (285ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (157ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (442ms)
		LoadAssemblies (275ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (226ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (621ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (465ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 7.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.5 MB). Loaded Objects now: 6920.
Memory consumption went from 167.1 MB to 164.5 MB.
Total: 25.858834 ms (FindLiveObjects: 2.733334 ms CreateObjectMapping: 0.917708 ms MarkObjects: 20.294000 ms  DeleteObjects: 1.913209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.244 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.600 seconds
Domain Reload Profiling: 1847ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (206ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (855ms)
		LoadAssemblies (587ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (312ms)
			TypeCache.Refresh (44ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (253ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (600ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (461ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (328ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (5.0 MB). Loaded Objects now: 6922.
Memory consumption went from 167.1 MB to 162.2 MB.
Total: 8.849791 ms (FindLiveObjects: 0.413542 ms CreateObjectMapping: 0.196458 ms MarkObjects: 6.001167 ms  DeleteObjects: 2.238000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.644 seconds
Refreshing native plugins compatible for Editor in 3.17 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.67 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.656 seconds
Domain Reload Profiling: 1304ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (373ms)
		LoadAssemblies (224ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (657ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (358ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (4.4 MB). Loaded Objects now: 6924.
Memory consumption went from 167.1 MB to 162.7 MB.
Total: 12.556917 ms (FindLiveObjects: 2.038583 ms CreateObjectMapping: 0.385833 ms MarkObjects: 6.759500 ms  DeleteObjects: 3.372125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.821 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.810 seconds
Domain Reload Profiling: 2635ms
	BeginReloadAssembly (1013ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (64ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (308ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (714ms)
		LoadAssemblies (916ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (811ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (590ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (447ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.6 MB). Loaded Objects now: 6926.
Memory consumption went from 167.2 MB to 163.5 MB.
Total: 11.722625 ms (FindLiveObjects: 0.667167 ms CreateObjectMapping: 0.826583 ms MarkObjects: 7.319625 ms  DeleteObjects: 2.908416 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.885 seconds
Refreshing native plugins compatible for Editor in 8.10 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 9.75 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.636 seconds
Domain Reload Profiling: 3527ms
	BeginReloadAssembly (826ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (92ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (329ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (989ms)
		LoadAssemblies (654ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (528ms)
			TypeCache.Refresh (84ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (421ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1638ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1222ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (949ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.8 MB). Loaded Objects now: 6929.
Memory consumption went from 167.2 MB to 164.4 MB.
Total: 42.750584 ms (FindLiveObjects: 3.824417 ms CreateObjectMapping: 0.554458 ms MarkObjects: 33.358500 ms  DeleteObjects: 5.012208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.515 seconds
Refreshing native plugins compatible for Editor in 2.99 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.701 seconds
Domain Reload Profiling: 2219ms
	BeginReloadAssembly (602ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (46ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (234ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (821ms)
		LoadAssemblies (726ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (270ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (213ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (529ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (388ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 6931.
Memory consumption went from 167.1 MB to 163.8 MB.
Total: 8.938750 ms (FindLiveObjects: 0.491959 ms CreateObjectMapping: 0.366208 ms MarkObjects: 5.363958 ms  DeleteObjects: 2.715792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2083.793029 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6d0e4d7bb11655f82d1400b9de36abb2') in 0.333159292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.692 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.648 seconds
Domain Reload Profiling: 1343ms
	BeginReloadAssembly (252ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (62ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (380ms)
		LoadAssemblies (264ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (156ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (133ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (649ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (373ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.3 MB). Loaded Objects now: 6953.
Memory consumption went from 171.6 MB to 167.3 MB.
Total: 11.830000 ms (FindLiveObjects: 0.752417 ms CreateObjectMapping: 0.434834 ms MarkObjects: 7.155625 ms  DeleteObjects: 3.485917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.026 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.872 seconds
Domain Reload Profiling: 1903ms
	BeginReloadAssembly (411ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (131ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (544ms)
		LoadAssemblies (410ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (238ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (873ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (637ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (464ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 2.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 6955.
Memory consumption went from 171.3 MB to 167.6 MB.
Total: 44.081583 ms (FindLiveObjects: 6.792875 ms CreateObjectMapping: 0.715709 ms MarkObjects: 30.078958 ms  DeleteObjects: 6.492708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.646 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.629 seconds
Domain Reload Profiling: 1279ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (376ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (630ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (463ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.6 MB). Loaded Objects now: 6957.
Memory consumption went from 171.2 MB to 165.6 MB.
Total: 10.248083 ms (FindLiveObjects: 0.387000 ms CreateObjectMapping: 0.240792 ms MarkObjects: 7.167500 ms  DeleteObjects: 2.451791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.782 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.86 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1439ms
	BeginReloadAssembly (247ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (478ms)
		LoadAssemblies (309ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (654ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (514ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.3 MB). Loaded Objects now: 6959.
Memory consumption went from 171.2 MB to 165.9 MB.
Total: 13.667541 ms (FindLiveObjects: 0.886167 ms CreateObjectMapping: 0.708833 ms MarkObjects: 7.945125 ms  DeleteObjects: 4.126000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.074 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.739 seconds
Domain Reload Profiling: 1817ms
	BeginReloadAssembly (460ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (135ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (545ms)
		LoadAssemblies (479ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (739ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (411ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 6961.
Memory consumption went from 171.1 MB to 168.1 MB.
Total: 14.353250 ms (FindLiveObjects: 0.613750 ms CreateObjectMapping: 0.449083 ms MarkObjects: 10.649292 ms  DeleteObjects: 2.640500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.671 seconds
Refreshing native plugins compatible for Editor in 4.36 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.756 seconds
Domain Reload Profiling: 1429ms
	BeginReloadAssembly (212ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (401ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (172ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (756ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (588ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (430ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 3.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 6963.
Memory consumption went from 171.2 MB to 167.8 MB.
Total: 11.520083 ms (FindLiveObjects: 0.681875 ms CreateObjectMapping: 0.421250 ms MarkObjects: 8.129667 ms  DeleteObjects: 2.286500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.700 seconds
Refreshing native plugins compatible for Editor in 3.54 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.623 seconds
Domain Reload Profiling: 1325ms
	BeginReloadAssembly (284ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (159ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (364ms)
		LoadAssemblies (234ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (469ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (334ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 6965.
Memory consumption went from 171.2 MB to 167.5 MB.
Total: 10.674792 ms (FindLiveObjects: 0.406792 ms CreateObjectMapping: 0.190917 ms MarkObjects: 5.855959 ms  DeleteObjects: 4.220333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.206 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.687 seconds
Domain Reload Profiling: 1896ms
	BeginReloadAssembly (550ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (218ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (598ms)
		LoadAssemblies (468ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (250ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (194ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (502ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 6967.
Memory consumption went from 171.2 MB to 167.7 MB.
Total: 12.114792 ms (FindLiveObjects: 0.916959 ms CreateObjectMapping: 0.485750 ms MarkObjects: 7.991709 ms  DeleteObjects: 2.719666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.631 seconds
Refreshing native plugins compatible for Editor in 7.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.382 seconds
Domain Reload Profiling: 2019ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (356ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1383ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (914ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (233ms)
			ProcessInitializeOnLoadAttributes (585ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 6969.
Memory consumption went from 171.2 MB to 168.2 MB.
Total: 14.887292 ms (FindLiveObjects: 1.329500 ms CreateObjectMapping: 0.515208 ms MarkObjects: 9.727041 ms  DeleteObjects: 3.314667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.9 MB). Loaded Objects now: 6969.
Memory consumption went from 161.4 MB to 157.5 MB.
Total: 59.811667 ms (FindLiveObjects: 0.888042 ms CreateObjectMapping: 0.273916 ms MarkObjects: 55.543542 ms  DeleteObjects: 3.098375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3274.257496 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff774297dde188865753a2803d412cfc') in 0.426123875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.185 seconds
Refreshing native plugins compatible for Editor in 3.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.49 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.866 seconds
Domain Reload Profiling: 2056ms
	BeginReloadAssembly (550ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (273ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (561ms)
		LoadAssemblies (453ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (867ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (627ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (440ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 6991.
Memory consumption went from 180.1 MB to 176.6 MB.
Total: 18.634833 ms (FindLiveObjects: 0.636083 ms CreateObjectMapping: 0.627208 ms MarkObjects: 13.265042 ms  DeleteObjects: 4.105750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 59.986269 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '55c5fa517b3de8519b165f283bb5399a') in 0.31823075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.690 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.81 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.635 seconds
Domain Reload Profiling: 1328ms
	BeginReloadAssembly (250ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (382ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (176ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (153ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (636ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (455ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7013.
Memory consumption went from 179.7 MB to 175.9 MB.
Total: 14.343584 ms (FindLiveObjects: 0.820125 ms CreateObjectMapping: 0.225625 ms MarkObjects: 9.684000 ms  DeleteObjects: 3.613000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.154 seconds
Refreshing native plugins compatible for Editor in 2.52 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.774 seconds
Domain Reload Profiling: 1932ms
	BeginReloadAssembly (442ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (128ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (604ms)
		LoadAssemblies (523ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (185ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (775ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (602ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (461ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.8 MB). Loaded Objects now: 7015.
Memory consumption went from 179.3 MB to 174.6 MB.
Total: 22.903125 ms (FindLiveObjects: 0.628708 ms CreateObjectMapping: 0.461125 ms MarkObjects: 11.968500 ms  DeleteObjects: 9.843416 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.645 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.603 seconds
Domain Reload Profiling: 1252ms
	BeginReloadAssembly (226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (364ms)
		LoadAssemblies (245ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (162ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (604ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (447ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.8 MB). Loaded Objects now: 7017.
Memory consumption went from 179.3 MB to 174.5 MB.
Total: 11.432833 ms (FindLiveObjects: 0.964375 ms CreateObjectMapping: 1.172666 ms MarkObjects: 6.674292 ms  DeleteObjects: 2.620666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.068 seconds
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.772 seconds
Domain Reload Profiling: 1845ms
	BeginReloadAssembly (409ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (35ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (153ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (597ms)
		LoadAssemblies (365ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (319ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (260ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (772ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (597ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (464ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7019.
Memory consumption went from 179.3 MB to 175.3 MB.
Total: 14.401500 ms (FindLiveObjects: 0.562250 ms CreateObjectMapping: 0.459125 ms MarkObjects: 10.384541 ms  DeleteObjects: 2.994500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1403.209951 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b83c0f8c3346d0591d0e745accec015a') in 0.336307666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.746 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.619 seconds
Domain Reload Profiling: 1367ms
	BeginReloadAssembly (284ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (124ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (403ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (619ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (479ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (356ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.6 MB). Loaded Objects now: 7041.
Memory consumption went from 183.7 MB to 179.1 MB.
Total: 8.570625 ms (FindLiveObjects: 0.541000 ms CreateObjectMapping: 0.291584 ms MarkObjects: 5.334291 ms  DeleteObjects: 2.402375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.644 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.597 seconds
Domain Reload Profiling: 1245ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (369ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (125ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (598ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (444ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (6.1 MB). Loaded Objects now: 7043.
Memory consumption went from 183.4 MB to 177.4 MB.
Total: 12.247708 ms (FindLiveObjects: 0.513750 ms CreateObjectMapping: 0.318750 ms MarkObjects: 8.055417 ms  DeleteObjects: 3.358625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.652 seconds
Refreshing native plugins compatible for Editor in 2.59 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.627 seconds
Domain Reload Profiling: 1282ms
	BeginReloadAssembly (205ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (392ms)
		LoadAssemblies (260ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (627ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7045.
Memory consumption went from 183.4 MB to 179.8 MB.
Total: 13.088250 ms (FindLiveObjects: 1.179542 ms CreateObjectMapping: 0.295958 ms MarkObjects: 8.893084 ms  DeleteObjects: 2.719333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.645 seconds
Refreshing native plugins compatible for Editor in 1.71 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.633 seconds
Domain Reload Profiling: 1282ms
	BeginReloadAssembly (222ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (367ms)
		LoadAssemblies (242ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (493ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (365ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7047.
Memory consumption went from 183.4 MB to 180.2 MB.
Total: 12.285083 ms (FindLiveObjects: 0.449083 ms CreateObjectMapping: 0.216917 ms MarkObjects: 7.475208 ms  DeleteObjects: 4.142625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.628 seconds
Refreshing native plugins compatible for Editor in 3.66 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.78 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.684 seconds
Domain Reload Profiling: 1316ms
	BeginReloadAssembly (193ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (384ms)
		LoadAssemblies (241ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (157ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (685ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (528ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (389ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7049.
Memory consumption went from 183.4 MB to 179.3 MB.
Total: 14.159958 ms (FindLiveObjects: 1.312084 ms CreateObjectMapping: 0.439541 ms MarkObjects: 8.936584 ms  DeleteObjects: 3.470959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.856 seconds
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.853 seconds
Domain Reload Profiling: 1713ms
	BeginReloadAssembly (338ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (219ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (450ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (854ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (682ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (516ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7051.
Memory consumption went from 183.3 MB to 180.1 MB.
Total: 17.538167 ms (FindLiveObjects: 0.582792 ms CreateObjectMapping: 0.978833 ms MarkObjects: 12.296791 ms  DeleteObjects: 3.678167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.633 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.70 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.603 seconds
Domain Reload Profiling: 1241ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (172ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (604ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (453ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (329ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 7053.
Memory consumption went from 183.4 MB to 179.5 MB.
Total: 15.404583 ms (FindLiveObjects: 0.603250 ms CreateObjectMapping: 0.369208 ms MarkObjects: 11.320500 ms  DeleteObjects: 3.109834 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.714 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.659 seconds
Domain Reload Profiling: 1377ms
	BeginReloadAssembly (238ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (416ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (660ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (373ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7055.
Memory consumption went from 183.4 MB to 179.2 MB.
Total: 13.378209 ms (FindLiveObjects: 0.579750 ms CreateObjectMapping: 0.462792 ms MarkObjects: 9.146958 ms  DeleteObjects: 3.187583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.649 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.84 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1245ms
	BeginReloadAssembly (215ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (380ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (593ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (455ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7057.
Memory consumption went from 183.4 MB to 179.6 MB.
Total: 10.598541 ms (FindLiveObjects: 0.483208 ms CreateObjectMapping: 0.317583 ms MarkObjects: 7.318250 ms  DeleteObjects: 2.479125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.664 seconds
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.631 seconds
Domain Reload Profiling: 1299ms
	BeginReloadAssembly (248ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (234ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (632ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (461ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (326ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 4.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 7059.
Memory consumption went from 183.4 MB to 179.5 MB.
Total: 10.692250 ms (FindLiveObjects: 2.527209 ms CreateObjectMapping: 0.302041 ms MarkObjects: 5.259500 ms  DeleteObjects: 2.602125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.203 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.88 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.711 seconds
Domain Reload Profiling: 1918ms
	BeginReloadAssembly (511ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (150ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (623ms)
		LoadAssemblies (575ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (275ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (213ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (711ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (559ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 6.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 7061.
Memory consumption went from 183.3 MB to 180.7 MB.
Total: 55.709167 ms (FindLiveObjects: 3.846750 ms CreateObjectMapping: 1.221209 ms MarkObjects: 41.104042 ms  DeleteObjects: 9.534917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1101.912906 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6c5479df58bc90dd78ff532da9739a6b') in 0.423444208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.853 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.655 seconds
Domain Reload Profiling: 1513ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (155ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (460ms)
		LoadAssemblies (296ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (218ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (656ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (468ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7083.
Memory consumption went from 187.8 MB to 184.7 MB.
Total: 17.878250 ms (FindLiveObjects: 1.035208 ms CreateObjectMapping: 0.439209 ms MarkObjects: 13.645250 ms  DeleteObjects: 2.757333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (6.9 MB). Loaded Objects now: 7083.
Memory consumption went from 178.0 MB to 171.2 MB.
Total: 100.062875 ms (FindLiveObjects: 0.882417 ms CreateObjectMapping: 0.218083 ms MarkObjects: 93.681125 ms  DeleteObjects: 5.280209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.070 seconds
Refreshing native plugins compatible for Editor in 2.37 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.713 seconds
Domain Reload Profiling: 1788ms
	BeginReloadAssembly (441ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (153ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (558ms)
		LoadAssemblies (499ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (222ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (715ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (387ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7085.
Memory consumption went from 187.4 MB to 184.0 MB.
Total: 8.704792 ms (FindLiveObjects: 1.096958 ms CreateObjectMapping: 0.345750 ms MarkObjects: 4.800250 ms  DeleteObjects: 2.460875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.660 seconds
Refreshing native plugins compatible for Editor in 4.73 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.637 seconds
Domain Reload Profiling: 1301ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (384ms)
		LoadAssemblies (247ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (176ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (153ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7087.
Memory consumption went from 187.4 MB to 184.4 MB.
Total: 12.967000 ms (FindLiveObjects: 2.125083 ms CreateObjectMapping: 0.236083 ms MarkObjects: 8.225125 ms  DeleteObjects: 2.380208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.807 seconds
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.639 seconds
Domain Reload Profiling: 1450ms
	BeginReloadAssembly (303ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (441ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (254ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (216ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (640ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (485ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7089.
Memory consumption went from 187.4 MB to 183.7 MB.
Total: 15.556833 ms (FindLiveObjects: 0.861000 ms CreateObjectMapping: 0.491667 ms MarkObjects: 11.027250 ms  DeleteObjects: 3.175041 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.126 seconds
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.58 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.874 seconds
Domain Reload Profiling: 2004ms
	BeginReloadAssembly (413ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (162ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (633ms)
		LoadAssemblies (452ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (284ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (874ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (686ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (517ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.1 MB). Loaded Objects now: 7091.
Memory consumption went from 187.4 MB to 185.3 MB.
Total: 16.541333 ms (FindLiveObjects: 2.115125 ms CreateObjectMapping: 2.063541 ms MarkObjects: 9.954958 ms  DeleteObjects: 2.406166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.117 seconds
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.821 seconds
Domain Reload Profiling: 1941ms
	BeginReloadAssembly (500ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (62ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (172ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (453ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (205ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (821ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (625ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (480ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7093.
Memory consumption went from 187.4 MB to 183.8 MB.
Total: 11.121208 ms (FindLiveObjects: 0.502584 ms CreateObjectMapping: 0.409791 ms MarkObjects: 7.470417 ms  DeleteObjects: 2.736917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.183 seconds
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.99 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.850 seconds
Domain Reload Profiling: 2037ms
	BeginReloadAssembly (606ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (226ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (509ms)
		LoadAssemblies (448ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (182ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (851ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (660ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (140ms)
			ProcessInitializeOnLoadAttributes (468ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.8 MB). Loaded Objects now: 7095.
Memory consumption went from 187.4 MB to 184.7 MB.
Total: 26.545833 ms (FindLiveObjects: 1.145500 ms CreateObjectMapping: 0.509708 ms MarkObjects: 19.518083 ms  DeleteObjects: 5.368209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.234 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.12 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.031 seconds
Domain Reload Profiling: 2268ms
	BeginReloadAssembly (468ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (68ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (149ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (705ms)
		LoadAssemblies (565ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1032ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (842ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (659ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7097.
Memory consumption went from 187.5 MB to 184.3 MB.
Total: 33.914708 ms (FindLiveObjects: 1.447750 ms CreateObjectMapping: 0.506584 ms MarkObjects: 29.203625 ms  DeleteObjects: 2.755500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.832 seconds
Refreshing native plugins compatible for Editor in 2.35 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.670 seconds
Domain Reload Profiling: 1505ms
	BeginReloadAssembly (320ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (172ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (444ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (232ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (477ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 5.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.7 MB). Loaded Objects now: 7099.
Memory consumption went from 187.4 MB to 181.7 MB.
Total: 13.839417 ms (FindLiveObjects: 0.538709 ms CreateObjectMapping: 0.351709 ms MarkObjects: 9.020875 ms  DeleteObjects: 3.924750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.208 seconds
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.04 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.367 seconds
Domain Reload Profiling: 2579ms
	BeginReloadAssembly (527ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (167ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (611ms)
		LoadAssemblies (472ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (271ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (219ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1367ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1121ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (930ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 5.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7101.
Memory consumption went from 187.4 MB to 183.9 MB.
Total: 54.266875 ms (FindLiveObjects: 3.136416 ms CreateObjectMapping: 1.433084 ms MarkObjects: 43.143334 ms  DeleteObjects: 6.552959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.857 seconds
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.50 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.690 seconds
Domain Reload Profiling: 1551ms
	BeginReloadAssembly (350ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (170ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (452ms)
		LoadAssemblies (288ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (205ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (690ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7103.
Memory consumption went from 187.4 MB to 184.4 MB.
Total: 14.925583 ms (FindLiveObjects: 0.637417 ms CreateObjectMapping: 0.412083 ms MarkObjects: 9.234917 ms  DeleteObjects: 4.640291 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.805 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.55 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.058 seconds
Domain Reload Profiling: 1867ms
	BeginReloadAssembly (261ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (484ms)
		LoadAssemblies (375ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1058ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (862ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (644ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.7 MB). Loaded Objects now: 7105.
Memory consumption went from 187.4 MB to 184.7 MB.
Total: 34.481708 ms (FindLiveObjects: 1.952625 ms CreateObjectMapping: 1.389042 ms MarkObjects: 22.269709 ms  DeleteObjects: 8.869291 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7386.045807 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ee582da9f173d67e244402a557d6c03f') in 0.379316333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.393 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.750 seconds
Domain Reload Profiling: 2146ms
	BeginReloadAssembly (689ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (122ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (251ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (623ms)
		LoadAssemblies (522ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (282ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (229ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (750ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (412ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7127.
Memory consumption went from 191.9 MB to 187.6 MB.
Total: 10.460583 ms (FindLiveObjects: 0.425084 ms CreateObjectMapping: 0.246000 ms MarkObjects: 7.422208 ms  DeleteObjects: 2.366875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.148 seconds
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.87 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.810 seconds
Domain Reload Profiling: 1963ms
	BeginReloadAssembly (527ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (170ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (558ms)
		LoadAssemblies (529ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (811ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (623ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 7129.
Memory consumption went from 191.5 MB to 189.0 MB.
Total: 27.991209 ms (FindLiveObjects: 1.185917 ms CreateObjectMapping: 2.348708 ms MarkObjects: 20.610542 ms  DeleteObjects: 3.844375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.179 seconds
Refreshing native plugins compatible for Editor in 6.23 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 7.27 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.929 seconds
Domain Reload Profiling: 2111ms
	BeginReloadAssembly (498ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (161ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (582ms)
		LoadAssemblies (559ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (226ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (701ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 5.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 7131.
Memory consumption went from 191.5 MB to 189.0 MB.
Total: 70.974458 ms (FindLiveObjects: 18.876583 ms CreateObjectMapping: 0.567625 ms MarkObjects: 42.203750 ms  DeleteObjects: 9.324833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3445.390426 seconds.
  path: Assets/Prefabs/SoulCreatureGiant_Ocean.prefab
  artifactKey: Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreatureGiant_Ocean.prefab using Guid(d605243c0d9f447bea1bc43fd37b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb09bbab981bc93c4b25cde871311cb7') in 0.441126875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.862 seconds
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.703 seconds
Domain Reload Profiling: 1569ms
	BeginReloadAssembly (360ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (99ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (138ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (440ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (211ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (521ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (383ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7153.
Memory consumption went from 195.9 MB to 192.3 MB.
Total: 27.532625 ms (FindLiveObjects: 0.435667 ms CreateObjectMapping: 0.344958 ms MarkObjects: 22.847334 ms  DeleteObjects: 3.901417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.076 seconds
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.666 seconds
Domain Reload Profiling: 1745ms
	BeginReloadAssembly (462ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (233ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (544ms)
		LoadAssemblies (407ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (287ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (217ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (494ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7155.
Memory consumption went from 195.5 MB to 191.8 MB.
Total: 12.026334 ms (FindLiveObjects: 0.974125 ms CreateObjectMapping: 0.293416 ms MarkObjects: 6.641458 ms  DeleteObjects: 4.116375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.476 seconds
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.853 seconds
Domain Reload Profiling: 2333ms
	BeginReloadAssembly (785ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (65ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (453ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (591ms)
		LoadAssemblies (435ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (855ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (667ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 6.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7157.
Memory consumption went from 195.5 MB to 192.1 MB.
Total: 34.867584 ms (FindLiveObjects: 1.313959 ms CreateObjectMapping: 0.566000 ms MarkObjects: 29.153041 ms  DeleteObjects: 3.832209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.691 seconds
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.697 seconds
Domain Reload Profiling: 1391ms
	BeginReloadAssembly (275ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (137ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (356ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (155ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (698ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.0 MB). Loaded Objects now: 7159.
Memory consumption went from 195.5 MB to 190.5 MB.
Total: 14.703167 ms (FindLiveObjects: 0.705791 ms CreateObjectMapping: 0.411333 ms MarkObjects: 8.796792 ms  DeleteObjects: 4.787958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.732 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.720 seconds
Domain Reload Profiling: 1455ms
	BeginReloadAssembly (255ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (145ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (400ms)
		LoadAssemblies (270ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (160ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (721ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (572ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (432ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 3.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7161.
Memory consumption went from 195.5 MB to 192.1 MB.
Total: 12.761708 ms (FindLiveObjects: 0.564250 ms CreateObjectMapping: 0.524417 ms MarkObjects: 9.091291 ms  DeleteObjects: 2.580500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.218 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.794 seconds
Domain Reload Profiling: 2015ms
	BeginReloadAssembly (578ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (184ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (547ms)
		LoadAssemblies (554ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (215ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (794ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (561ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (412ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.3 MB). Loaded Objects now: 7163.
Memory consumption went from 195.5 MB to 191.2 MB.
Total: 19.828875 ms (FindLiveObjects: 0.906584 ms CreateObjectMapping: 0.450625 ms MarkObjects: 15.734834 ms  DeleteObjects: 2.735541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.651 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.681 seconds
Domain Reload Profiling: 1335ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (354ms)
		LoadAssemblies (250ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (148ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (128ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (681ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (494ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.7 MB). Loaded Objects now: 7165.
Memory consumption went from 195.5 MB to 190.7 MB.
Total: 8.329500 ms (FindLiveObjects: 0.524375 ms CreateObjectMapping: 0.231000 ms MarkObjects: 5.251042 ms  DeleteObjects: 2.322083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (4.7 MB). Loaded Objects now: 7165.
Memory consumption went from 186.3 MB to 181.6 MB.
Total: 30.963583 ms (FindLiveObjects: 3.605542 ms CreateObjectMapping: 0.341666 ms MarkObjects: 20.897042 ms  DeleteObjects: 6.117959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.333 seconds
Refreshing native plugins compatible for Editor in 7.35 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.99 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.079 seconds
Domain Reload Profiling: 2414ms
	BeginReloadAssembly (561ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (188ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (673ms)
		LoadAssemblies (565ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (305ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (250ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1079ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (483ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 7167.
Memory consumption went from 195.5 MB to 192.4 MB.
Total: 11.350000 ms (FindLiveObjects: 0.790792 ms CreateObjectMapping: 0.376167 ms MarkObjects: 7.803875 ms  DeleteObjects: 2.378792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.398 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.793 seconds
Domain Reload Profiling: 2197ms
	BeginReloadAssembly (552ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (39ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (224ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (769ms)
		LoadAssemblies (569ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (385ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (297ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (793ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (563ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (425ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 7169.
Memory consumption went from 195.5 MB to 192.9 MB.
Total: 13.678083 ms (FindLiveObjects: 0.847667 ms CreateObjectMapping: 0.620458 ms MarkObjects: 9.560709 ms  DeleteObjects: 2.648542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.169 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.835 seconds
Domain Reload Profiling: 2013ms
	BeginReloadAssembly (552ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (164ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (524ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (214ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (836ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (656ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (507ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7171.
Memory consumption went from 195.5 MB to 192.4 MB.
Total: 15.933250 ms (FindLiveObjects: 0.793709 ms CreateObjectMapping: 0.391625 ms MarkObjects: 12.284709 ms  DeleteObjects: 2.462583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.781 seconds
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.664 seconds
Domain Reload Profiling: 1447ms
	BeginReloadAssembly (310ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (147ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (293ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (664ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (507ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (372ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.8 MB). Loaded Objects now: 7173.
Memory consumption went from 195.5 MB to 192.7 MB.
Total: 39.769375 ms (FindLiveObjects: 15.253250 ms CreateObjectMapping: 0.925666 ms MarkObjects: 13.600500 ms  DeleteObjects: 9.988750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.228 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.62 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.828 seconds
Domain Reload Profiling: 2059ms
	BeginReloadAssembly (598ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (263ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (552ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (204ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (828ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (634ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (117ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7175.
Memory consumption went from 195.5 MB to 192.2 MB.
Total: 12.010583 ms (FindLiveObjects: 1.224333 ms CreateObjectMapping: 1.852250 ms MarkObjects: 6.850667 ms  DeleteObjects: 2.081458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.641 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.788 seconds
Domain Reload Profiling: 1432ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (347ms)
		LoadAssemblies (260ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (145ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (119ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (789ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (620ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (460ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.4 MB). Loaded Objects now: 7177.
Memory consumption went from 195.5 MB to 193.1 MB.
Total: 18.825083 ms (FindLiveObjects: 1.662459 ms CreateObjectMapping: 2.304000 ms MarkObjects: 12.736417 ms  DeleteObjects: 2.121459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.139 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.792 seconds
Domain Reload Profiling: 1936ms
	BeginReloadAssembly (522ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (202ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (547ms)
		LoadAssemblies (455ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (244ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (793ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (590ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (445ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7179.
Memory consumption went from 195.6 MB to 192.2 MB.
Total: 15.544125 ms (FindLiveObjects: 0.527166 ms CreateObjectMapping: 0.421959 ms MarkObjects: 11.339666 ms  DeleteObjects: 3.254750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.915 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.75 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.777 seconds
Domain Reload Profiling: 1696ms
	BeginReloadAssembly (367ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (165ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (487ms)
		LoadAssemblies (358ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (778ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (622ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (464ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7181.
Memory consumption went from 195.6 MB to 192.3 MB.
Total: 12.138334 ms (FindLiveObjects: 0.541875 ms CreateObjectMapping: 0.435084 ms MarkObjects: 8.947500 ms  DeleteObjects: 2.212583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.841 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 8.31 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.806 seconds
Domain Reload Profiling: 1651ms
	BeginReloadAssembly (335ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (214ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (442ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (222ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (806ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (631ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (460ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.1 MB). Loaded Objects now: 7183.
Memory consumption went from 195.6 MB to 192.4 MB.
Total: 26.708459 ms (FindLiveObjects: 0.427791 ms CreateObjectMapping: 0.337792 ms MarkObjects: 20.967917 ms  DeleteObjects: 4.974292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.737 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.680 seconds
Domain Reload Profiling: 1419ms
	BeginReloadAssembly (249ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (429ms)
		LoadAssemblies (286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (680ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (386ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7185.
Memory consumption went from 195.5 MB to 191.5 MB.
Total: 10.983333 ms (FindLiveObjects: 1.519583 ms CreateObjectMapping: 0.286250 ms MarkObjects: 6.465958 ms  DeleteObjects: 2.710583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.265 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.704 seconds
Domain Reload Profiling: 1973ms
	BeginReloadAssembly (622ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (212ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (501ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (705ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (521ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7187.
Memory consumption went from 195.6 MB to 191.9 MB.
Total: 10.498292 ms (FindLiveObjects: 0.635875 ms CreateObjectMapping: 0.358541 ms MarkObjects: 7.011709 ms  DeleteObjects: 2.491083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.597 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.76 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.706 seconds
Domain Reload Profiling: 1305ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (158ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (133ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (706ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (553ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (419ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.0 MB). Loaded Objects now: 7189.
Memory consumption went from 195.6 MB to 190.5 MB.
Total: 7.766834 ms (FindLiveObjects: 0.388000 ms CreateObjectMapping: 0.299208 ms MarkObjects: 5.001209 ms  DeleteObjects: 2.077667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.440 seconds
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.774 seconds
Domain Reload Profiling: 2217ms
	BeginReloadAssembly (574ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (108ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (207ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (798ms)
		LoadAssemblies (643ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (291ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (238ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (775ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (599ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (451ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7191.
Memory consumption went from 195.6 MB to 191.6 MB.
Total: 11.169042 ms (FindLiveObjects: 0.713042 ms CreateObjectMapping: 0.445584 ms MarkObjects: 7.632375 ms  DeleteObjects: 2.377375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.970 seconds
Refreshing native plugins compatible for Editor in 3.21 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.57 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.689 seconds
Domain Reload Profiling: 1664ms
	BeginReloadAssembly (421ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (236ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (485ms)
		LoadAssemblies (355ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (226ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (691ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (503ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (359ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.6 MB). Loaded Objects now: 7193.
Memory consumption went from 195.6 MB to 191.0 MB.
Total: 23.748833 ms (FindLiveObjects: 0.610459 ms CreateObjectMapping: 1.611375 ms MarkObjects: 17.602375 ms  DeleteObjects: 3.923666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.915 seconds
Refreshing native plugins compatible for Editor in 2.40 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.86 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.749 seconds
Domain Reload Profiling: 1668ms
	BeginReloadAssembly (297ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (559ms)
		LoadAssemblies (426ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (231ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (749ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (593ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (441ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.8 MB). Loaded Objects now: 7195.
Memory consumption went from 195.5 MB to 192.7 MB.
Total: 17.043625 ms (FindLiveObjects: 0.630417 ms CreateObjectMapping: 0.408375 ms MarkObjects: 11.155625 ms  DeleteObjects: 4.847625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.675 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.752 seconds
Domain Reload Profiling: 1430ms
	BeginReloadAssembly (246ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (131ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (154ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (752ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (579ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (433ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7197.
Memory consumption went from 195.6 MB to 191.9 MB.
Total: 10.641000 ms (FindLiveObjects: 1.590708 ms CreateObjectMapping: 0.383916 ms MarkObjects: 6.216583 ms  DeleteObjects: 2.448583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.365 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.709 seconds
Domain Reload Profiling: 2078ms
	BeginReloadAssembly (636ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (59ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (203ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (655ms)
		LoadAssemblies (605ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (251ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (710ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (538ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7199.
Memory consumption went from 195.6 MB to 191.8 MB.
Total: 9.438750 ms (FindLiveObjects: 0.536417 ms CreateObjectMapping: 0.354000 ms MarkObjects: 6.126167 ms  DeleteObjects: 2.421417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.669 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.676 seconds
Domain Reload Profiling: 1347ms
	BeginReloadAssembly (180ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (428ms)
		LoadAssemblies (317ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (148ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (125ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (677ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (533ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (413ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.9 MB). Loaded Objects now: 7201.
Memory consumption went from 195.6 MB to 190.7 MB.
Total: 7.416875 ms (FindLiveObjects: 0.393208 ms CreateObjectMapping: 0.325667 ms MarkObjects: 4.519625 ms  DeleteObjects: 2.177791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.894 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.667 seconds
Domain Reload Profiling: 1564ms
	BeginReloadAssembly (245ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (561ms)
		LoadAssemblies (439ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (668ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7203.
Memory consumption went from 195.6 MB to 192.6 MB.
Total: 14.199791 ms (FindLiveObjects: 2.688916 ms CreateObjectMapping: 0.540584 ms MarkObjects: 8.106667 ms  DeleteObjects: 2.862625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.2 MB). Loaded Objects now: 7203.
Memory consumption went from 186.4 MB to 183.2 MB.
Total: 53.138458 ms (FindLiveObjects: 0.646417 ms CreateObjectMapping: 0.420458 ms MarkObjects: 49.648875 ms  DeleteObjects: 2.421541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.612 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.614 seconds
Domain Reload Profiling: 2234ms
	BeginReloadAssembly (736ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (415ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (822ms)
		LoadAssemblies (631ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (343ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (266ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (467ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7205.
Memory consumption went from 195.5 MB to 192.2 MB.
Total: 8.657667 ms (FindLiveObjects: 0.469666 ms CreateObjectMapping: 0.283459 ms MarkObjects: 5.072333 ms  DeleteObjects: 2.831333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.626 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.39 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.645 seconds
Domain Reload Profiling: 1273ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (343ms)
		LoadAssemblies (252ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (145ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (120ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (645ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (501ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (372ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.3 MB). Loaded Objects now: 7207.
Memory consumption went from 195.6 MB to 191.2 MB.
Total: 8.895667 ms (FindLiveObjects: 0.416291 ms CreateObjectMapping: 0.345000 ms MarkObjects: 5.639042 ms  DeleteObjects: 2.494334 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (5.7 MB). Loaded Objects now: 7207.
Memory consumption went from 186.4 MB to 180.8 MB.
Total: 114.280708 ms (FindLiveObjects: 0.603458 ms CreateObjectMapping: 0.277209 ms MarkObjects: 108.361375 ms  DeleteObjects: 5.037917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.392 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.707 seconds
Domain Reload Profiling: 2102ms
	BeginReloadAssembly (596ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (46ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (221ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (730ms)
		LoadAssemblies (617ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (274ms)
			TypeCache.Refresh (51ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (208ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (707ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (550ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (406ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7209.
Memory consumption went from 195.6 MB to 192.0 MB.
Total: 10.165334 ms (FindLiveObjects: 0.619000 ms CreateObjectMapping: 0.483333 ms MarkObjects: 6.370667 ms  DeleteObjects: 2.691833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.634 seconds
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.617 seconds
Domain Reload Profiling: 1254ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (243ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (166ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (617ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.5 MB). Loaded Objects now: 7211.
Memory consumption went from 195.6 MB to 190.1 MB.
Total: 7.921791 ms (FindLiveObjects: 0.379917 ms CreateObjectMapping: 0.339791 ms MarkObjects: 4.813875 ms  DeleteObjects: 2.387625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.263 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.702 seconds
Domain Reload Profiling: 1969ms
	BeginReloadAssembly (623ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (272ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (567ms)
		LoadAssemblies (473ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (200ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (388ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.1 MB). Loaded Objects now: 7213.
Memory consumption went from 195.6 MB to 190.5 MB.
Total: 11.073791 ms (FindLiveObjects: 1.001208 ms CreateObjectMapping: 0.371333 ms MarkObjects: 5.793250 ms  DeleteObjects: 3.906875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.668 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.660 seconds
Domain Reload Profiling: 1331ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (450ms)
		LoadAssemblies (297ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (155ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (660ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (515ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (398ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.8 MB). Loaded Objects now: 7215.
Memory consumption went from 195.5 MB to 190.8 MB.
Total: 7.212209 ms (FindLiveObjects: 0.448292 ms CreateObjectMapping: 0.195292 ms MarkObjects: 4.596791 ms  DeleteObjects: 1.971417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  3.752 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.89 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.737 seconds
Domain Reload Profiling: 4493ms
	BeginReloadAssembly (2652ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (262ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (861ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (1038ms)
		LoadAssemblies (1644ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (738ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (546ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (402ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.9 MB). Loaded Objects now: 7217.
Memory consumption went from 195.5 MB to 190.6 MB.
Total: 11.110208 ms (FindLiveObjects: 0.567625 ms CreateObjectMapping: 0.382958 ms MarkObjects: 7.796667 ms  DeleteObjects: 2.361916 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.766 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.741 seconds
Domain Reload Profiling: 1511ms
	BeginReloadAssembly (210ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (496ms)
		LoadAssemblies (375ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (173ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (742ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (592ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (441ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.5 MB). Loaded Objects now: 7219.
Memory consumption went from 195.5 MB to 190.1 MB.
Total: 6.698250 ms (FindLiveObjects: 0.393250 ms CreateObjectMapping: 0.294000 ms MarkObjects: 3.981666 ms  DeleteObjects: 2.028875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.560 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.697 seconds
Domain Reload Profiling: 2266ms
	BeginReloadAssembly (654ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (93ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (234ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (823ms)
		LoadAssemblies (604ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (352ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (295ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (698ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (524ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (399ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.9 MB). Loaded Objects now: 7221.
Memory consumption went from 195.6 MB to 191.7 MB.
Total: 8.499292 ms (FindLiveObjects: 0.567541 ms CreateObjectMapping: 0.196917 ms MarkObjects: 5.308791 ms  DeleteObjects: 2.425625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.100 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.95 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.746 seconds
Domain Reload Profiling: 1849ms
	BeginReloadAssembly (416ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (147ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (609ms)
		LoadAssemblies (439ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (272ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (226ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (746ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (442ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7223.
Memory consumption went from 195.6 MB to 192.0 MB.
Total: 17.006375 ms (FindLiveObjects: 0.431375 ms CreateObjectMapping: 0.697292 ms MarkObjects: 13.053000 ms  DeleteObjects: 2.823917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.324 seconds
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.760 seconds
Domain Reload Profiling: 2086ms
	BeginReloadAssembly (742ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (39ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (211ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (495ms)
		LoadAssemblies (491ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (760ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (570ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (426ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.9 MB). Loaded Objects now: 7225.
Memory consumption went from 195.6 MB to 189.7 MB.
Total: 9.899834 ms (FindLiveObjects: 0.420750 ms CreateObjectMapping: 0.315666 ms MarkObjects: 6.185625 ms  DeleteObjects: 2.977167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.401 seconds
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.33 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.701 seconds
Domain Reload Profiling: 2106ms
	BeginReloadAssembly (709ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (201ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (624ms)
		LoadAssemblies (753ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7227.
Memory consumption went from 195.6 MB to 191.5 MB.
Total: 8.489500 ms (FindLiveObjects: 0.508250 ms CreateObjectMapping: 0.340792 ms MarkObjects: 5.328667 ms  DeleteObjects: 2.311042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.309 seconds
Refreshing native plugins compatible for Editor in 5.39 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.808 seconds
Domain Reload Profiling: 2123ms
	BeginReloadAssembly (596ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (70ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (267ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (633ms)
		LoadAssemblies (550ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (212ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (811ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (599ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (432ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 7.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7229.
Memory consumption went from 195.6 MB to 192.6 MB.
Total: 33.829917 ms (FindLiveObjects: 2.544208 ms CreateObjectMapping: 1.546375 ms MarkObjects: 23.702375 ms  DeleteObjects: 6.036000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.648 seconds
Refreshing native plugins compatible for Editor in 9.68 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.35 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.005 seconds
Domain Reload Profiling: 2659ms
	BeginReloadAssembly (773ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (240ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (751ms)
		LoadAssemblies (666ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (337ms)
			TypeCache.Refresh (72ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1005ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (709ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (509ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7231.
Memory consumption went from 195.6 MB to 192.3 MB.
Total: 19.493541 ms (FindLiveObjects: 0.498458 ms CreateObjectMapping: 0.425000 ms MarkObjects: 15.916833 ms  DeleteObjects: 2.652375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 12875.849766 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80043c28c2332eace8c44859ce402dab') in 0.45333275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.947 seconds
Refreshing native plugins compatible for Editor in 9.24 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.59 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.806 seconds
Domain Reload Profiling: 2755ms
	BeginReloadAssembly (1065ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (87ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (518ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (125ms)
	LoadAllAssembliesAndSetupDomain (700ms)
		LoadAssemblies (640ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (305ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (807ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (629ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (481ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7253.
Memory consumption went from 200.0 MB to 196.7 MB.
Total: 25.154416 ms (FindLiveObjects: 1.247417 ms CreateObjectMapping: 0.427041 ms MarkObjects: 20.480042 ms  DeleteObjects: 2.998792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.431 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.81 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.270 seconds
Domain Reload Profiling: 2704ms
	BeginReloadAssembly (665ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (125ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (224ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (669ms)
		LoadAssemblies (555ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (289ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1270ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (829ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (634ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7255.
Memory consumption went from 199.6 MB to 196.6 MB.
Total: 18.527583 ms (FindLiveObjects: 0.940625 ms CreateObjectMapping: 1.139375 ms MarkObjects: 12.975167 ms  DeleteObjects: 3.471292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.955 seconds
Refreshing native plugins compatible for Editor in 4.55 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.780 seconds
Domain Reload Profiling: 1740ms
	BeginReloadAssembly (368ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (146ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (522ms)
		LoadAssemblies (421ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (202ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (780ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (567ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (424ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 8.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.2 MB). Loaded Objects now: 7257.
Memory consumption went from 199.7 MB to 197.4 MB.
Total: 45.937750 ms (FindLiveObjects: 2.819834 ms CreateObjectMapping: 2.310375 ms MarkObjects: 35.062875 ms  DeleteObjects: 5.743667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.314 seconds
Refreshing native plugins compatible for Editor in 2.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.693 seconds
Domain Reload Profiling: 2011ms
	BeginReloadAssembly (635ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (67ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (246ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (547ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (239ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (693ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (516ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (383ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.6 MB). Loaded Objects now: 7259.
Memory consumption went from 199.6 MB to 195.0 MB.
Total: 17.257125 ms (FindLiveObjects: 1.376292 ms CreateObjectMapping: 0.454875 ms MarkObjects: 11.681292 ms  DeleteObjects: 3.738750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.641 seconds
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.736 seconds
Domain Reload Profiling: 1380ms
	BeginReloadAssembly (210ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (371ms)
		LoadAssemblies (267ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (127ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (737ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (576ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (424ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.2 MB). Loaded Objects now: 7261.
Memory consumption went from 199.6 MB to 194.4 MB.
Total: 9.242792 ms (FindLiveObjects: 0.441625 ms CreateObjectMapping: 0.247791 ms MarkObjects: 6.362250 ms  DeleteObjects: 2.190125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.137 seconds
Refreshing native plugins compatible for Editor in 1.78 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.77 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.878 seconds
Domain Reload Profiling: 2020ms
	BeginReloadAssembly (524ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (144ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (194ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (880ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (678ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (103ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 3.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7263.
Memory consumption went from 199.6 MB to 196.3 MB.
Total: 35.330250 ms (FindLiveObjects: 1.496375 ms CreateObjectMapping: 0.536792 ms MarkObjects: 28.190666 ms  DeleteObjects: 5.104584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.294 seconds
Refreshing native plugins compatible for Editor in 3.50 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.222 seconds
Domain Reload Profiling: 2520ms
	BeginReloadAssembly (510ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (50ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (668ms)
		LoadAssemblies (470ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (330ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (264ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1223ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (926ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (726ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 7.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.6 MB). Loaded Objects now: 7265.
Memory consumption went from 199.7 MB to 197.0 MB.
Total: 51.910625 ms (FindLiveObjects: 10.970958 ms CreateObjectMapping: 2.692667 ms MarkObjects: 32.276791 ms  DeleteObjects: 5.969167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.095 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.019 seconds
Domain Reload Profiling: 2118ms
	BeginReloadAssembly (499ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (261ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (517ms)
		LoadAssemblies (462ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1019ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (836ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (638ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.7 MB). Loaded Objects now: 7267.
Memory consumption went from 199.7 MB to 196.0 MB.
Total: 23.018958 ms (FindLiveObjects: 1.218917 ms CreateObjectMapping: 0.556542 ms MarkObjects: 17.687750 ms  DeleteObjects: 3.552750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.587 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.820 seconds
Domain Reload Profiling: 2410ms
	BeginReloadAssembly (860ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (88ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (291ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (658ms)
		LoadAssemblies (610ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (821ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (633ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (489ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7269.
Memory consumption went from 199.7 MB to 196.3 MB.
Total: 15.851417 ms (FindLiveObjects: 0.536667 ms CreateObjectMapping: 0.486917 ms MarkObjects: 10.885208 ms  DeleteObjects: 3.941708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.909 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.95 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.754 seconds
Domain Reload Profiling: 1666ms
	BeginReloadAssembly (363ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (197ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (481ms)
		LoadAssemblies (342ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (754ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (586ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (446ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.8 MB). Loaded Objects now: 7271.
Memory consumption went from 199.7 MB to 195.8 MB.
Total: 9.746500 ms (FindLiveObjects: 0.443042 ms CreateObjectMapping: 0.343625 ms MarkObjects: 6.607000 ms  DeleteObjects: 2.351708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.287 seconds
Refreshing native plugins compatible for Editor in 3.47 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.10 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.787 seconds
Domain Reload Profiling: 2079ms
	BeginReloadAssembly (536ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (174ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (662ms)
		LoadAssemblies (602ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (248ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (588ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (438ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 5.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7273.
Memory consumption went from 199.6 MB to 196.3 MB.
Total: 29.757875 ms (FindLiveObjects: 1.651792 ms CreateObjectMapping: 0.517625 ms MarkObjects: 22.714208 ms  DeleteObjects: 4.872709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.689 seconds
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.704 seconds
Domain Reload Profiling: 1397ms
	BeginReloadAssembly (230ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (399ms)
		LoadAssemblies (252ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (554ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (408ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.5 MB). Loaded Objects now: 7275.
Memory consumption went from 199.7 MB to 195.2 MB.
Total: 11.727625 ms (FindLiveObjects: 0.579041 ms CreateObjectMapping: 0.273542 ms MarkObjects: 8.382250 ms  DeleteObjects: 2.491959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.181 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.85 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 2119ms
	BeginReloadAssembly (562ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (177ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (541ms)
		LoadAssemblies (505ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (185ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (936ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (739ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (587ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 3.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.9 MB). Loaded Objects now: 7277.
Memory consumption went from 199.7 MB to 196.8 MB.
Total: 29.845209 ms (FindLiveObjects: 1.386917 ms CreateObjectMapping: 1.439208 ms MarkObjects: 24.071334 ms  DeleteObjects: 2.946625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.731 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.67 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.756 seconds
Domain Reload Profiling: 1490ms
	BeginReloadAssembly (268ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (144ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (402ms)
		LoadAssemblies (285ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (757ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (584ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.3 MB). Loaded Objects now: 7279.
Memory consumption went from 199.7 MB to 196.4 MB.
Total: 10.339459 ms (FindLiveObjects: 0.372584 ms CreateObjectMapping: 0.303208 ms MarkObjects: 6.567292 ms  DeleteObjects: 3.095917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.197 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.39 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.834 seconds
Domain Reload Profiling: 2037ms
	BeginReloadAssembly (540ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (150ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (578ms)
		LoadAssemblies (512ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (252ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (836ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (637ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (470ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.6 MB). Loaded Objects now: 7281.
Memory consumption went from 199.7 MB to 196.1 MB.
Total: 16.532292 ms (FindLiveObjects: 1.342542 ms CreateObjectMapping: 0.404583 ms MarkObjects: 11.950708 ms  DeleteObjects: 2.833667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.742 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.684 seconds
Domain Reload Profiling: 1429ms
	BeginReloadAssembly (268ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (141ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (408ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (685ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (531ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (394ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.9 MB). Loaded Objects now: 7283.
Memory consumption went from 199.6 MB to 194.7 MB.
Total: 7.478500 ms (FindLiveObjects: 0.461416 ms CreateObjectMapping: 0.229292 ms MarkObjects: 4.499042 ms  DeleteObjects: 2.288042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.185 seconds
Refreshing native plugins compatible for Editor in 3.36 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.43 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.890 seconds
Domain Reload Profiling: 2078ms
	BeginReloadAssembly (584ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (88ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (193ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (528ms)
		LoadAssemblies (466ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (223ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (891ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (710ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (535ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 7285.
Memory consumption went from 199.7 MB to 197.2 MB.
Total: 31.864542 ms (FindLiveObjects: 0.905250 ms CreateObjectMapping: 0.487166 ms MarkObjects: 27.556625 ms  DeleteObjects: 2.914541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.647 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.701 seconds
Domain Reload Profiling: 1353ms
	BeginReloadAssembly (212ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (105ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (369ms)
		LoadAssemblies (261ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (156ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (126ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (703ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (415ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.1 MB). Loaded Objects now: 7287.
Memory consumption went from 199.7 MB to 194.5 MB.
Total: 8.350125 ms (FindLiveObjects: 0.484291 ms CreateObjectMapping: 0.191875 ms MarkObjects: 5.100458 ms  DeleteObjects: 2.573041 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (5.0 MB). Loaded Objects now: 7287.
Memory consumption went from 190.6 MB to 185.6 MB.
Total: 61.054167 ms (FindLiveObjects: 6.570375 ms CreateObjectMapping: 0.599334 ms MarkObjects: 48.686292 ms  DeleteObjects: 5.197500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 22.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (4.1 MB). Loaded Objects now: 7287.
Memory consumption went from 190.6 MB to 186.5 MB.
Total: 75.584417 ms (FindLiveObjects: 1.681125 ms CreateObjectMapping: 0.791083 ms MarkObjects: 70.568250 ms  DeleteObjects: 2.542875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.213 seconds
Refreshing native plugins compatible for Editor in 3.60 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.758 seconds
Domain Reload Profiling: 1975ms
	BeginReloadAssembly (503ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (178ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (636ms)
		LoadAssemblies (585ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (43ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (758ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (415ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.5 MB). Loaded Objects now: 7289.
Memory consumption went from 199.7 MB to 197.2 MB.
Total: 36.487250 ms (FindLiveObjects: 1.504875 ms CreateObjectMapping: 0.472250 ms MarkObjects: 30.113833 ms  DeleteObjects: 4.395333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.803 seconds
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.700 seconds
Domain Reload Profiling: 1507ms
	BeginReloadAssembly (258ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (124ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (701ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (398ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.1 MB). Loaded Objects now: 7291.
Memory consumption went from 199.7 MB to 195.6 MB.
Total: 11.304833 ms (FindLiveObjects: 0.745959 ms CreateObjectMapping: 0.444375 ms MarkObjects: 8.029208 ms  DeleteObjects: 2.084708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.198 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.834 seconds
Domain Reload Profiling: 2036ms
	BeginReloadAssembly (582ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (213ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (539ms)
		LoadAssemblies (511ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (835ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (642ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (486ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.2 MB). Loaded Objects now: 7293.
Memory consumption went from 199.6 MB to 197.4 MB.
Total: 30.542666 ms (FindLiveObjects: 2.974625 ms CreateObjectMapping: 1.970250 ms MarkObjects: 21.022209 ms  DeleteObjects: 4.574583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.730 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.669 seconds
Domain Reload Profiling: 1402ms
	BeginReloadAssembly (266ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (142ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (404ms)
		LoadAssemblies (254ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (519ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (377ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7295.
Memory consumption went from 199.7 MB to 195.5 MB.
Total: 8.487000 ms (FindLiveObjects: 1.329833 ms CreateObjectMapping: 0.244833 ms MarkObjects: 4.895667 ms  DeleteObjects: 2.015791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (2.2 MB). Loaded Objects now: 7295.
Memory consumption went from 190.6 MB to 188.5 MB.
Total: 85.268292 ms (FindLiveObjects: 1.250083 ms CreateObjectMapping: 0.412875 ms MarkObjects: 71.976334 ms  DeleteObjects: 11.626625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.784 seconds
Refreshing native plugins compatible for Editor in 8.61 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 10.53 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.491 seconds
Domain Reload Profiling: 3282ms
	BeginReloadAssembly (945ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (101ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (397ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (736ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (312ms)
			TypeCache.Refresh (51ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1492ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1211ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (953ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7297.
Memory consumption went from 199.7 MB to 196.2 MB.
Total: 27.125250 ms (FindLiveObjects: 1.005667 ms CreateObjectMapping: 0.475500 ms MarkObjects: 21.174041 ms  DeleteObjects: 4.469250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6082 unused Assets / (3.8 MB). Loaded Objects now: 7297.
Memory consumption went from 190.6 MB to 186.8 MB.
Total: 60.938833 ms (FindLiveObjects: 2.643416 ms CreateObjectMapping: 0.657375 ms MarkObjects: 50.476625 ms  DeleteObjects: 7.159958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  2.580 seconds
Refreshing native plugins compatible for Editor in 4.49 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 10.88 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.574 seconds
Domain Reload Profiling: 4174ms
	BeginReloadAssembly (1367ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (65ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (730ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1082ms)
		LoadAssemblies (1007ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (445ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (360ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1575ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1192ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (192ms)
			ProcessInitializeOnLoadAttributes (928ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 3.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.2 MB). Loaded Objects now: 7299.
Memory consumption went from 199.7 MB to 196.5 MB.
Total: 42.727250 ms (FindLiveObjects: 1.237542 ms CreateObjectMapping: 1.427583 ms MarkObjects: 35.031625 ms  DeleteObjects: 5.029417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.329 seconds
Refreshing native plugins compatible for Editor in 2.33 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.72 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.857 seconds
Domain Reload Profiling: 2190ms
	BeginReloadAssembly (671ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (265ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (584ms)
		LoadAssemblies (531ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (246ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (858ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (662ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (504ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.5 MB). Loaded Objects now: 7301.
Memory consumption went from 199.7 MB to 196.2 MB.
Total: 25.568583 ms (FindLiveObjects: 0.900791 ms CreateObjectMapping: 0.474792 ms MarkObjects: 20.834167 ms  DeleteObjects: 3.356750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.665 seconds
Refreshing native plugins compatible for Editor in 2.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.700 seconds
Domain Reload Profiling: 1369ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (385ms)
		LoadAssemblies (242ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (700ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (533ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (5.4 MB). Loaded Objects now: 7303.
Memory consumption went from 199.7 MB to 194.3 MB.
Total: 11.810084 ms (FindLiveObjects: 0.624375 ms CreateObjectMapping: 0.191833 ms MarkObjects: 6.873584 ms  DeleteObjects: 4.119208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.620 seconds
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.856 seconds
Domain Reload Profiling: 2483ms
	BeginReloadAssembly (702ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (83ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (241ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (808ms)
		LoadAssemblies (785ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (856ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (617ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (455ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.0 MB). Loaded Objects now: 7305.
Memory consumption went from 199.7 MB to 195.7 MB.
Total: 21.138542 ms (FindLiveObjects: 1.131417 ms CreateObjectMapping: 0.377250 ms MarkObjects: 16.844708 ms  DeleteObjects: 2.783833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.323 seconds
Refreshing native plugins compatible for Editor in 11.01 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 6.92 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.273 seconds
Domain Reload Profiling: 2601ms
	BeginReloadAssembly (468ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (251ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (759ms)
		LoadAssemblies (603ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (270ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (232ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1274ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (985ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (733ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (2ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 3.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.1 MB). Loaded Objects now: 7307.
Memory consumption went from 199.7 MB to 197.6 MB.
Total: 27.944916 ms (FindLiveObjects: 0.652750 ms CreateObjectMapping: 0.373750 ms MarkObjects: 23.981084 ms  DeleteObjects: 2.936625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.713 seconds
Refreshing native plugins compatible for Editor in 1.69 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.671 seconds
Domain Reload Profiling: 2388ms
	BeginReloadAssembly (836ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (308ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (793ms)
		LoadAssemblies (826ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7309.
Memory consumption went from 199.7 MB to 195.5 MB.
Total: 9.104083 ms (FindLiveObjects: 0.639083 ms CreateObjectMapping: 0.199750 ms MarkObjects: 5.571583 ms  DeleteObjects: 2.692667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  0.894 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.29 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.803 seconds
Domain Reload Profiling: 1701ms
	BeginReloadAssembly (281ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (138ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (553ms)
		LoadAssemblies (399ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (228ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (803ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (621ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (4.2 MB). Loaded Objects now: 7311.
Memory consumption went from 199.7 MB to 195.5 MB.
Total: 14.312333 ms (FindLiveObjects: 0.538333 ms CreateObjectMapping: 3.421666 ms MarkObjects: 8.148000 ms  DeleteObjects: 2.201875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.709 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.867 seconds
Domain Reload Profiling: 2580ms
	BeginReloadAssembly (916ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (396ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (705ms)
		LoadAssemblies (643ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (272ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (202ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (867ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (670ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (497ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.4 MB). Loaded Objects now: 7313.
Memory consumption went from 199.7 MB to 196.2 MB.
Total: 28.214500 ms (FindLiveObjects: 1.355834 ms CreateObjectMapping: 0.427875 ms MarkObjects: 21.975625 ms  DeleteObjects: 4.454250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16bc13000 may have been prematurely finalized
- Loaded All Assemblies, in  1.401 seconds
Refreshing native plugins compatible for Editor in 2.45 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.71 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.895 seconds
Domain Reload Profiling: 2299ms
	BeginReloadAssembly (717ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (52ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (290ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (555ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (275ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (224ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (896ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (683ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (527ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 4.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (3.0 MB). Loaded Objects now: 7315.
Memory consumption went from 199.7 MB to 196.7 MB.
Total: 34.649000 ms (FindLiveObjects: 0.936500 ms CreateObjectMapping: 1.839416 ms MarkObjects: 29.214542 ms  DeleteObjects: 2.657042 ms)

Prepare: number of updated asset objects reloaded= 0
